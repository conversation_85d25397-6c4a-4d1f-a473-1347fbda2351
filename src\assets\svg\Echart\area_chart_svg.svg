<?xml version="1.0" encoding="UTF-8"?>
<svg width="42px" height="34px" viewBox="0 0 42 34" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title></title>
    <defs>
        <rect id="path-1" x="0" y="0" width="42" height="34"></rect>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="55.6139074%" id="linearGradient-3">
            <stop stop-color="#19AAF0" stop-opacity="0.256747159" offset="0%"></stop>
            <stop stop-color="#19AAF0" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="39.7675737%" x2="48.2133881%" y2="67.5810573%" id="linearGradient-4">
            <stop stop-color="#17CAC5" stop-opacity="0.207085883" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="应用管理主页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="05报表设计-02" transform="translate(-309.000000, -344.000000)">
            <g id="图表类型选择" transform="translate(248.000000, 232.000000)">
                <g id="面积图" transform="translate(60.000000, 105.000000)">
                    <g id="面积图" transform="translate(1.000000, 7.000000)">
                        <mask id="mask-2" fill="white">
                            <use xlink:href="#path-1"></use>
                        </mask>
                        <g id="蒙版"></g>
                        <polygon id="路径-6备份-3" fill="url(#linearGradient-3)" mask="url(#mask-2)" points="0 20.4097713 11 10 23.4594595 15.5166902 33.6756757 11 42 16.1552104 42 35 0 35"></polygon>
                        <polyline id="路径-6" stroke="#1CCBCC" stroke-linecap="round" mask="url(#mask-2)" points="0 27 9.83783784 21.4 23.4594595 21.8 33.6756757 17 42 23"></polyline>
                        <polyline id="路径-6备份-2" stroke="#19AAF0" stroke-linecap="round" mask="url(#mask-2)" points="0 20 11 10 23.4594595 15.32 33.6756757 11 42 16.04"></polyline>
                        <polygon id="路径-6备份" fill="url(#linearGradient-4)" mask="url(#mask-2)" points="-1.45716772e-16 26.421756 9.83783784 21.1606045 23.4594595 21.5364011 34 17 42 23 42 36 0 36"></polygon>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>