<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>切片</title>
    <defs>
        <filter color-interpolation-filters="auto" id="filter-1">
            <feColorMatrix in="SourceGraphic" type="matrix" values="0 0 0 0 0.184314 0 0 0 0 0.325490 0 0 0 0 0.917647 0 0 0 1.000000 0"></feColorMatrix>
        </filter>
    </defs>
    <g id="二级门户-自定义模块" stroke-width="1" opacity="0.3">
        <g id="005应用商城-应用详情页-展开-暂定" transform="translate(-296.000000, -438.000000)">
            <g id="主要内容" transform="translate(216.000000, 95.000000)">
                <g id="热门应用" transform="translate(32.000000, 209.000000)">
                    <g id="普通表单-展开" transform="translate(0.000000, 93.000000)">
                        <g id="1.图标Iconography/2.面性/5文件备份-3" transform="translate(48.000000, 39.000000)" >
                            <g transform="translate(0.000000, 2.000000)">
                                <path d="M13,1 C13.552,1 14,1.448 14,2 L14,2 L14,11 L10,15 L3,15 C2.448,15 2,14.552 2,14 L2,14 L2,2 C2,1.448 2.448,1 3,1 L3,1 Z M8.05005,8 L4.95005,8 C4.70155,8 4.50005,8.2235 4.50005,8.5 C4.50005,8.7765 4.70155,9 4.95005,9 L4.95005,9 L8.05005,9 C8.29855,9 8.50005,8.7765 8.50005,8.5 C8.50005,8.2235 8.29855,8 8.05005,8 L8.05005,8 Z M11.05005,6 L4.95005,6 C4.70155,6 4.50005,6.2235 4.50005,6.5 C4.50005,6.7765 4.70155,7 4.95005,7 L4.95005,7 L11.05005,7 C11.29855,7 11.50005,6.7765 11.50005,6.5 C11.50005,6.2235 11.29855,6 11.05005,6 L11.05005,6 Z M11.05005,4 L4.95005,4 C4.70155,4 4.50005,4.2235 4.50005,4.5 C4.50005,4.7765 4.70155,5 4.95005,5 L4.95005,5 L11.05005,5 C11.29855,5 11.50005,4.7765 11.50005,4.5 C11.50005,4.2235 11.29855,4 11.05005,4 L11.05005,4 Z" id="形状结合" ></path>
                                <path d="M10,15 L10,12 C10,11.448 10.448,11 11,11 L14,11 L10,15 Z" id="Fill-5"></path>
                            </g>
                        </g>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>