import { request } from '@/fawkes'
import type { IPageInfo, IResponse } from '@/types'
import type {
  IQueryItem,
  ISlotButtonItem,
  ITableButtonItem,
  IColumnItem,
  ITableDataItem,
  IFilterListItem
} from '@/modules/Application/AppModule/type'
import { ISysFormVisibleRange } from './dashboard'
import { IPrintTemplate, ITreeTable } from './appModuleDesigner.type'
interface IQueryPageInfo {
  innerBtnTable: ISlotButtonItem[]
  funcBtnTable: ITableButtonItem[]
  columnConfigList: IColumnItem[]
  treeTable?: ITreeTable
  formKey: string
  formType: '0' | '1' | '2'
  groupAdmin: boolean
  listName: string
  printTemplates: IPrintTemplate[]
  sysFormVisibleRange?: ISysFormVisibleRange
}

interface IActiveField {
  browser_name: boolean
  create_date: boolean
  fill_in_device: boolean
  ip_address: boolean
  operating_system: boolean
  process_state: boolean
  [key: string]: boolean
}

interface IColumn {
  browser_name: string
  create_date: string
  fill_in_device: string
  ip_address: string
  operating_system: string
  process_state: string
  [key: string]: string
}

interface ITagAttribute {
  version: string
  [key: string]: string
}

interface ITableData {
  activeField: IActiveField
  column: IColumn
  formType: string
  page: IPageInfo<ITableDataItem>
  tagAttribute: ITagAttribute
}

//接口请求数据

export interface ITableDataParam {
  page: number
  size: number
  formKey: string
  query: IQueryItem[]
  order: string
}
export interface IExportDataParam extends ITableDataParam {
  columnStr?: string
}

export interface IDeleteDataParam {
  formKey: string
  id: string
}

export interface IBatchDeleteDataParam {
  formKey: string
  idStr: string
}

export function queryPageInfo(id: string) {
  return request<IResponse<IQueryPageInfo>>({
    url: '/sys-form/list/config',
    method: 'get',
    params: { id }
  })
}
export function queryTableData(data: ITableDataParam) {
  return request<IResponse<ITableData>>({
    url: '/sys-form/form/dynamicData',
    method: 'post',
    data: data
  })
}
export function deleteData(params: IDeleteDataParam) {
  return request<IResponse<null>>({
    url: '/sys-form/form/data',
    method: 'delete',
    params: params
  })
}
export function batchDeleteData(params: IBatchDeleteDataParam) {
  return request<IResponse<null>>({
    url: '/sys-form/form/data/multi',
    method: 'delete',
    params: params
  })
}
export function exportData(data: IExportDataParam) {
  return request<Blob>({
    url: '/sys-form/form/excel/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}

//等待合并代码 肛泰 列表设计
// 查询模块设计信息
export function getDesignedModule(formKey: string) {
  return request<IResponse<any>>({
    url: '/sys-form/form/release',
    method: 'get',
    params: { formKey }
  })
}

// 获取关联数据
export function queryLinkage(formKey: string, data: ITableDataParam) {
  return request({
    url: '/sys-form/form/data/linkage',
    method: 'post',
    params: { formKey },
    data
  })
}

//获取筛选字段
export function queryFilterList(formKey: string) {
  return request<IResponse<IFilterListItem[]>>({
    url: '/sys-form/form/filter',
    method: 'get',
    params: { formKey }
  })
}

interface IQueryOptionsData {
  fieldId: string
  id: string
}
export function querySelectOptions(params: IQueryOptionsData) {
  return request({
    url: '/sys-form/form/dropdownSelect',
    method: 'get',
    params
  })
}

interface IQuerySelectConnectorResult {
  formKey: string
  field: string
}

export function querySelectConnectorResult(params: IQuerySelectConnectorResult) {
  return request<IResponse<any>>({
    url: '/sys-form/form/connector/select',
    method: 'get',
    params
  })
}
