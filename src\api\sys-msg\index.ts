/*
 * @Author: <EMAIL>
 * @Date: 2020-09-17 09:36:15
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2020-10-14 08:36:14
 * @Description: 消息相关接口
 */
import { request } from '@/fawkes'
import { IPageResponse, IResponse } from '@/types'

export interface IMsgParams {
  clientId?: string // 应用id
  userName?: string
  currentPage?: number //页码
  endDate?: string //结束日期
  pageSize?: number //每页条数
  searchValue?: string //消息内容
  startDate?: string //开始日期
  status?: 0 | -1 //状态(0:已读，-1:未读)
  type?: 100 | 200 | 300 //类型(100:公告，200:业务，300:私信)（可以查询多种类型，以英文逗号隔开即可）
}

export interface IMsg {
  id: string //记录的唯一键id
  fromTenantId: string //发送者租户id
  fromClientId: string //发送者应用id
  fromUserId: string //发送者用户id
  fromUserName: string //发送者用户名
  fromUserFullname: string //发送者全名
  msgSendType: number //消息发送方式（0站内信，1短信，2邮件）
  msgType: number //消息类型
  toClientId: string //接收者应用id
  toUsers: string //接收者列表（根据发送方式的不同会发生变化，站内信为用户名，短信为手机号，邮件为邮箱，以,隔开）
  msgTemplateId: string //消息模板id（不填会根据消息发送方式选择对应的默认模板）
  msgTemplateParam: string //消息模板变量填充值键值对（没有设定值的变量会使用模板中的默认值）
  avatarToken: string //头像文件token
  msgBody: string //站内信内容
  msgSendStatus: string //消息发送状态
  receiveLogId: string //消息接收记录id（当发送给多个人时每个人的收到的站内信id是相同的，但receiveLogId是不同的）
  status: number //消息状态（0已读，-1未读，-2删除）
  toUserId: string //当前接收者用户id
  toUserName: string //当前接收者用户名
  createDate: string
}
/**
 * @description: 根据用户ID和name获得所有消息
 */
export function queryMsgByIdAndName(data: IMsgParams) {
  return request<IPageResponse<IMsg>>({
    url: '/sys-msg/socketMsg/page',
    method: 'get',
    params: data
  })
}

/**
 * @description: 根据信息ID更新状态
 * @param   status 状态(0:已读,-1:未读,-2:删除)
 * @param   receiveLogId 站内信接收记录id
 */
export function updateMsgStatusById(data: { receiveLogIdList?: string; status?: 0 | -1 | -2 }) {
  return request<IResponse<null>>({
    url: '/sys-msg/socketMsg/updateStatus',
    method: 'POST',
    params: {
      receiveLogIdList: data.receiveLogIdList,
      status: data.status
    }
  })
}

interface IQueryMsgItemParam {
  currentPage?: string // 页码
  pageSize?: string //每页条数
  sort?: string //排序规则
  status?: string //状态
  endDate?: string // 结束日期
  startDate?: string // 开始日期
  columnName?: string // 排序字段
  msgSendTitle?: string // 消息发送标题
  msgSendType?: string // 消息发送类型 0-站内信 1-短信 2-邮件 3-极光推送 4-钉钉 5-企业微信 6-个推 7-飞书
}

export interface IMsgBody {
  fromUserName: string
  id: string
  msgBody: string
  msgTitle: string
  sendTime: string
  status: number
}
// 获取各种服务器消息发送结果列表
export function queryMsgList(params: IQueryMsgItemParam) {
  return request<IPageResponse<IMsgBody>>({
    url: '/sys-msg/msg/result/list',
    method: 'get',
    params: params
  })
}

interface IQueryMsgParam {
  currentPage?: string //页码
  pageSize?: string //每页条数
  searchValue?: string //搜索条件
  sort?: string //排序规则
  status?: string //状态
  username?: string //用户名
  msgId?: string //消息id
  columnName?: string // 排序字段
}

interface IMsgDetail {
  errorLog: string | null
  getAccountNumber: string
  getTime: string
  status: number
  userFullname: string
}
/**
 * 根据消息ID查询发送结果列表
 * @param params IQueryMsgParam
 * @returns 消息列表
 */
export function queryMsgDetailList(params: IQueryMsgParam) {
  return request<IPageResponse<IMsgDetail>>({
    url: '/sys-msg/msg/result/detail',
    method: 'get',
    params: params
  })
}

export interface IMsgItem {
  id?: string //记录的唯一键id
  fromTenantId?: number | string //发送者租户id
  fromClientId?: string //发送者应用id
  fromUserId?: string //发送者用户id
  fromUserName?: string //发送者用户名
  fromUserFullname?: string //发送者全名
  msgSendType?: number //消息发送方式（0站内信，1短信，2邮件）
  msgType?: number //消息类型
  toClientId?: string //接收者应用id
  toUsers?: string //接收者列表（根据发送方式的不同会发生变化，站内信为用户名，短信为手机号，邮件为邮箱，以,隔开）
  msgTemplateId?: string //消息模板id（不填会根据消息发送方式选择对应的默认模板）
  msgTemplateParam?: string //消息模板变量填充值键值对（没有设定值的变量会使用模板中的默认值）
  msgBody?: string //站内信内容
  msgSendStatus?: string //消息发送状
}
// 通用发送消息接口
export function addMsg(data: IMsgItem) {
  return request<IResponse>({
    method: 'POST',
    url: '/sys-msg/sendMsg',
    data
  })
}
interface IMsgTemplate {
  serverId: string
  currentPage: number
  pageSize: number
  templateName: string
  supportedMsgSendTypes: string
  sort: string
  columnName: string
}
export interface IMsgTemplateInfo {
  example: string
  id: string
  menuId: string
  portalId: string
  supportedMsgSendTypes: string
  supportedMsgTypes: string
  template: string
  templateName: string
  templateParamDefaultValue: string
  templateParamList: string
}
export interface IMsgTemplateForm {
  serverId?: string
  serverTemplateId?: string
  supportedMsgSendTypes: string
  templateName: string
  templateParamDefaultValue: string
  templateParamList: string
  supportedMsgTypes?: string
  template?: string
  msgType?: []
  paramsList?: []
  defaultParams?: []
  example?: string
  id?: string
  menuId?: string
  portalId?: string
}
interface ISendMsgInfo {
  fromClientId: string
  fromTenantId: number
  fromUserFullname: string
  fromUserId: string
  fromUserName: string
  msgSendType: string
  msgTemplateId: string
  msgType: string
  toClientId: string
  toUsers: string
}
//获取消息模板列表
export function queryMsgTemplateList(params: IMsgTemplate) {
  return request<IPageResponse<IMsgTemplateInfo>>({
    method: 'get',
    url: '/sys-msg/templates/page',
    params: params
  })
}
interface SmsParams {
  id: string
}
export function queryMsgTemplateById(params: SmsParams) {
  return request<IResponse<IMsgTemplateInfo>>({
    method: 'get',
    url: '/sys-msg/templates',
    params: params
  })
}
interface IDeleteParams {
  id: string
}
//根据分类目录ID删除
export function delTemplateById(params: IDeleteParams) {
  return request<IResponse<null>>({
    method: 'DELETE',
    url: '/sys-msg/templates',
    params: params
  })
}
export function addTemplate(data: IMsgTemplateForm) {
  return request<IResponse<null>>({
    method: 'POST',
    url: '/sys-msg/templates',
    data
  })
}
export function updateTemplate(data: IMsgTemplateForm) {
  return request<IResponse<null>>({
    method: 'PUT',
    url: '/sys-msg/templates',
    data
  })
}
interface IDefaultParams {
  msgSendType: string
}
export function queryDefaultParams(params: IDefaultParams) {
  return request<IResponse<string>>({
    method: 'get',
    url: '/sys-msg/templates/defaultParams',
    params: params
  })
}
//发送消息
export function postMsg(data: ISendMsgInfo) {
  return request<IResponse<string>>({
    method: 'POST',
    url: '/sys-msg/sendMsg',
    data
  })
}
