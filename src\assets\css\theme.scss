// 上导航彩色配置
[data-name=wp] #fks-app.hideSidebar {
  .head-navbar-container {
    background: mix(#000, $theme, 10%);

    .portal {
      .portal-title,
      .fks-icon-arrow-down {
        color: #fff !important;
      }
    }

    .fks-menu.fks-menu--horizontal {
      background: transparent !important;

      .fks-menu-item {
        color: #fff !important;
        background: transparent !important;

        &:hover,
        &.is-active {
          background: var(--theme-color);
        }
      }

      .fks-submenu {
        .fks-submenu__title {
          color: #fff !important;
          background: transparent !important;

          i {
            color: #fff !important;
          }
        }

        &:hover,
        &.is-active {
          background: var(--theme-color);
        }
      }
    }

    .right-menu {
      .right-menu-item {
        svg,
        span {
          color: #fff ;
        }
      }
      .fks-icon-developerMode {
        color: #fff;
      }
    }
  }
}
