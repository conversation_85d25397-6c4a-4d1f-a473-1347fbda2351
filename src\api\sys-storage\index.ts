/*
 * by: chen ming
 * time: 2019/12/2
 * desc: 文件相关
 * */
import { request } from '@/fawkes'
import { IResponse } from '@/types'
import { AxiosResponse } from 'axios'
import { getSign } from '@/fawkes/request/sign'

interface IUploadFileParam {
  e9y?: string //服务器名
  file?: File //file
  g9s?: string //文件组
  md5?: string //md5
  tag?: string //标签
}
export interface IFileData {
  link: string //文件url链接
  fileToken: string //文件token
  fileName: string //文件名
  extName: string //文件拓展名
  groupToken: string //文件组名
  dir: string //所在文件夹名
  size: string //文件大小
  version: string //文件版本
  objectName: string //存储服务器中的文件名
  resource: Blob //文件字节流
}
//上传文件
export function uploadFile(data: IUploadFileParam) {
  const form = new FormData()

  for (const key in data) {
    form.append(key, data[key])
  }

  return request<IResponse<IFileData>>({
    url: '/sys-storage/upload',
    method: 'post',
    data: form,
    timeout: 0
  })
}

interface IGetFileInfoParam {
  f8s?: string[] //文件token列表
  g9s?: string[] //文件组名列表
}

// 根据文件token和grouptoken获取文件信息,data中传至少传g9s数组和f8s数组中的一个
export function queryFile(data: IGetFileInfoParam) {
  return request<IResponse<IFileData[]>>({
    url: '/sys-storage/file',
    method: 'post',
    data
  })
}
export function getFileAsPdf(fileToken: string) {
  return request<AxiosResponse<Blob>>({
    method: 'get',
    url: '/sys-storage/file/pdf',
    responseType: 'blob',
    params: {
      fileToken
    }
  })
}
// 根据文件token和grouptoken删除文件，token删除单文件，grouptoken删除文件组
export function deleteFile(data: IGetFileInfoParam) {
  return request<IResponse<null>>({
    url: '/sys-storage/file',
    method: 'delete',
    data
  })
}

//根据fileToken下载文件
export function downloadFile(f8s: string) {
  return request<AxiosResponse<Blob>>({
    url: '/sys-storage/download',
    method: 'GET',
    params: {
      f8s
    },
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    timeout: 0,
    responseType: 'blob'
  })
}

//根据fileToken下载图片
export function downloadImage(f8s: string) {
  return request<AxiosResponse<Blob>>({
    method: 'get',
    url: '/sys-storage/download_image',
    responseType: 'blob',
    params: {
      f8s
    }
  })
}

//根据groupToken下载zip
export function downloadZip(groupToken: string) {
  return request<AxiosResponse<Blob>>({
    url: '/sys-storage/zip',
    method: 'get',
    params: { groupToken },
    responseType: 'blob'
  })
}

//修改已上传文件信息
export function updateFileG9s(groupToken: string, data: string[]) {
  return request<IResponse<null>>({
    url: `/sys-storage/file/token?groupToken=${groupToken}`,
    method: 'put',
    data: data
  })
}

//上传图片识别文字
export function orc(data: { files: File }) {
  return request<IResponse>({
    url: '/sys-storage/ocr',
    method: 'post',
    data,
    timeout: 0
  })
}
//将g9s 和文件绑定
export function changeFileG9s(groupToken: string, data: string[]) {
  return request({
    url: '/sys-storage/file/token',
    method: 'put',
    params: getSign({ groupToken }),
    data: data
  })
}
