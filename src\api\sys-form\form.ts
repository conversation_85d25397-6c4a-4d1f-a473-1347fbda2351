import { request } from '@/fawkes'
import { getSign } from '@/fawkes/request/sign'
import { IResponse } from '@/types/'

import { IComponentName } from '@/api/sys-form/custom-app'
import { IQueryItem, ITableDataItem } from '@/modules/Application/AppModule/type'
import { IFormData, ILinkageData } from '@/modules/FormCenter/componentsWp/Form/type'
import { IApproval } from '@/modules/FormCenter/componentsWp/CustomAssignee/type'
import { IModule } from '@/api/sys-form/appModuleDesigner.type'
import { IFieldOptions } from '@/modules/WorkPackage/type'

export interface IQueryDetailTableColumnParams {
  formKey: string
  version: string
  detailTableField: string
}

export interface IColumnOptions {
  tagAttribute: IComponentName
  label: string
  prop: string
}

//查询明细表的列
export function queryDetailTableColumn(params: IQueryDetailTableColumnParams): Promise<IResponse<IColumnOptions[]>> {
  return request<IResponse<IColumnOptions[]>>({
    url: '/sys-form/form/detailTable/column',
    method: 'get',
    params
  })
}

export interface IModuleOptions {
  id: string
  formDesign: IFieldOptions
}

//查询模块设计信息
export function queryFormRelease(formKey: string) {
  return request<IResponse<IModule>>({
    url: '/sys-form/form/release',
    method: 'get',
    params: { formKey }
  })
}
//todo:类型冲突太多，以后再合并,暂时定义两个
//查询模块设计信息
export function queryFormRelease2(params: { formKey: string }): Promise<IResponse<IModuleOptions>> {
  return request<IResponse<IModuleOptions>>({
    url: '/sys-form/form/release',
    method: 'get',
    params
  })
}

interface IQueryAssociateDataParam {
  query: IQueryItem[]
  order: string
}

interface IAssociateDataColumn {
  create_date: string | null
  [key: string]: string
}

export interface IAssociateData {
  column: IAssociateDataColumn
  fields: ITableDataItem[]
  tagAttribute: IAssociateDataColumn
}

// 获取关联数据
export async function queryAssociateData(formKey: string, data: IQueryAssociateDataParam) {
  const res = await request<IResponse<ILinkageData>>({
    url: '/sys-form/form/data/linkage',
    method: 'post',
    params: {
      formKey
    },
    data
  })
  return res.data
}

//获取表单配置传参
interface IQueryHistoryCustomFormConfig {
  bizId: string
  formKey: string
}

//获取表单填写数据传参
interface IQueryCustomFormParam {
  formKey: string
  id: string
  taskId: string
}

//获取历史表单配置
export function queryHistoryCustomFormConfig(params: IQueryHistoryCustomFormConfig) {
  return request<IResponse<IModule>>({
    url: '/sys-form/form/history',
    method: 'get',
    params
  })
}
//获取表单填写数据
export function queryCustomForm(params: IQueryCustomFormParam) {
  return request<IResponse<IQueryCustomForm>>({
    url: '/sys-form/form/data',
    method: 'get',
    params
  })
}

interface IQueryCustomForm {
  id: string
  create_by_full_name?: string
  create_by?: string
  create_date?: string
  update_by_full_name?: string
  update_by?: string
  update_date?: string
  process_state: string
  delete_flag: string
  [key: string]: string
}

// 自由流程表单 新增/更新 通用接口
export function submitCustomForm(data: ISubmitCustomFormParam) {
  return request({
    url: '/sys-form/form/data',
    method: 'post',
    data
  })
}

interface ISubmitCustomFormParam {
  formCommitParam: IFormCommitParam
  formProcessParam: IformProcessParam
}

interface IFormCommitParam {
  formData: IFormData
  formKey: string
}

interface IformProcessParam {
  bizId: string
  comment: string
  formKey: string
  modelKey: string
  processInstanceId: string
  stageFlag: number
  taskId: string
  variable: IVariable
  sysBpmCustomNodeDetailList: IApproval[]
}

interface IVariable extends IFormData {
  fawkes_custom_flow_start?: string
  fawkes_custom_flow_start_fullName?: string
}
interface IPrintParams {
  fileToken: string
  fileName: string
  formKey: string
  id: string
}
// 根据模板打印数据
export function printTemplate(data: IPrintParams) {
  return request<Blob>({
    url: '/sys-form/form/print/template',
    method: 'get',
    params: data,
    responseType: 'blob'
  })
}
export interface IAddressItem {
  label: string
  code: string
  children: IAddressItem[]
}
//查询地址
export function queryAddress(type: string) {
  return request<IResponse<IAddressItem[]>>({
    url: '/sys-form/district',
    method: 'get',
    params: getSign({ type })
  })
}
