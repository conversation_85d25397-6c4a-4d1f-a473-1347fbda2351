import { request } from '@/fawkes'
import { IResponse } from '@/types'

export interface ITemplateOptions {
  id: string
  template: string | null
  templateName: string
  templateParamList: string
}

export interface IQueryTemplateListParams {
  smsServerId: string
  currentPage: number
  pageSize: number
  templateName: string
  supportedMsgSendTypes: number
  sort: string
  columnName: string
}

//查询消息模板列表
export function queryTemplateList(params: IQueryTemplateListParams): Promise<
  IResponse<{
    list: ITemplateOptions[]
  }>
> {
  return request<
    IResponse<{
      list: ITemplateOptions[]
    }>
  >({
    url: '/sys-msg/templates/page',
    method: 'get',
    params
  })
}

//查询消息模板详情
export function queryTemplateDetailById(params: { id: string }): Promise<IResponse<ITemplateOptions>> {
  return request<IResponse<ITemplateOptions>>({
    url: 'sys-msg/templates',
    method: 'get',
    params
  })
}
