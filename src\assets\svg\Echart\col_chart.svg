<?xml version="1.0" encoding="UTF-8"?>
<svg width="85px" height="58px" viewBox="0 0 85 58" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title></title>
    <g id="应用管理主页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="05-报表统计-05" transform="translate(-1690.000000, -247.000000)" fill="#FFC12E">
            <g id="编组-4备份-3" transform="translate(1517.000000, 181.000000)">
                <g id="条形图" transform="translate(173.500000, 66.500000)">
                    <rect id="矩形" opacity="0.149999991" transform="translate(44.000000, 4.500000) rotate(-270.000000) translate(-44.000000, -4.500000) " x="40" y="-35" width="8" height="79" rx="4"></rect>
                    <rect id="矩形备份-11" transform="translate(17.000000, 4.500000) rotate(-270.000000) translate(-17.000000, -4.500000) " x="13" y="-12" width="8" height="33" rx="4"></rect>
                    <rect id="矩形备份-5" opacity="0.149999991" transform="translate(44.000000, 20.500000) rotate(-270.000000) translate(-44.000000, -20.500000) " x="40" y="-19" width="8" height="79" rx="4"></rect>
                    <rect id="矩形备份-6" opacity="0.149999991" transform="translate(44.000000, 36.500000) rotate(-270.000000) translate(-44.000000, -36.500000) " x="40" y="-3" width="8" height="79" rx="4"></rect>
                    <rect id="矩形备份-7" opacity="0.149999991" transform="translate(44.000000, 52.500000) rotate(-270.000000) translate(-44.000000, -52.500000) " x="40" y="13" width="8" height="79" rx="4"></rect>
                    <rect id="矩形备份-12" transform="translate(27.000000, 20.500000) rotate(-270.000000) translate(-27.000000, -20.500000) " x="23" y="-6" width="8" height="53" rx="4"></rect>
                    <rect id="矩形备份-14" transform="translate(22.500000, 52.500000) rotate(-270.000000) translate(-22.500000, -52.500000) " x="18.5" y="30.5" width="8" height="44" rx="4"></rect>
                    <rect id="矩形备份-13" transform="translate(34.000000, 36.500000) rotate(-270.000000) translate(-34.000000, -36.500000) " x="30" y="3" width="8" height="67" rx="4"></rect>
                </g>
            </g>
        </g>
    </g>
</svg>