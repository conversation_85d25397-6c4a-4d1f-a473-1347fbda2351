@import './variables.scss';
@import './mixin.scss';
@import './layout.scss';
// @import "./theme.scss";
@import './mobileAdaptation.scss';
@import './global.scss';
@import './newFont.scss';
html,
body {
  position: relative;
  width: 100% !important;
  height: 100%;
  padding: 0 !important;
  margin: 0 !important;
  overflow: hidden;
  text-rendering: optimizeLegibility;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;

  ::v-deep .fks-select-dropdown.fks-popper.select-lang {
    //  background-color: #f9f9f9;
    margin-top: 10px;
  }
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

html,
body,
div,
ul,
li,
h1,
h2,
h3,
h4,
h5,
h6,
p,
dl,
dt,
dd,
ol,
form,
input,
textarea,
th,
td,
select,
button {
  font-family: inherit;
}

a,
a:focus,
a:hover {
  text-decoration: none;
  color: inherit;
  cursor: pointer;
}

a:focus,
a:active {
  outline: none;
}

div:focus {
  outline: none;
}

ul {
  padding: 0;
  margin: 0;
}

ul li {
  list-style-type: none;
}

th {
  font-weight: normal;
}

.clearfix {
  &::after {
    content: ' ';
    clear: both;
    display: block;
    height: 0;
    visibility: hidden;
    font-size: 0;
  }
}

/* 单查询框 */
.single-searchBox {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  font-size: 0;

  .single-searchBox-item {
    width: 300px;
    margin-right: 10px;
  }
}

//菜单栏popover样式
.toolbar-popover.fks-popover {
  padding: 8px;
}

// 菜单管理的多级下拉框
.portal-popper-cascader {
  .fks-cascader-menu__wrap {
    height: unset;
  }
}

// 表单公共样式
.form-ps {
  padding: 14px;
  border: 1px solid rgba(250, 237, 237, 1);
  border-left: 4px solid #f56c6c;
  font-size: 12px;
  line-height: 17px;
  // 表单中的注解，使用时加上此类名
  color: #191919;
  background: rgba(255, 250, 250, 1);

  p {
    margin: 0;
  }

  span {
    font-size: 14px;
    color: #393e45;
  }
}

/* 顶部间距8vh时弹窗高度 */
.dialog-8vh .fks-dialog__body {
  max-height: calc(92vh - 175px) !important;
}

//弹窗头部icon
.fks-dialog .fks-dialog__header {
  display: flex;
  align-items: center;
  color: #191919;

  i:not(.fks-dialog__close) {
    margin-right: 8px;
    font-size: 20px;
  }
}
.fks-query-page .fks-query-list-name {
  margin-bottom: 8px;
}
//表格滚动条样式优化
.fks-table--scrollable-y .fks-table__body-wrapper {
  overflow-y: overlay !important;
  .fks-table__body {
    width: 100% !important;
  }
}

//问号提示样式
.fks-icon-question {
  color: #ccc;
}

//tooltip样式
.fks-tooltip__popper.is-dark {
  color: #555 !important;
  background: #f5f5f5 !important;
  &[x-placement^='top'] {
    .popper__arrow {
      border-top-color: #f5f5f5 !important;
    }

    .popper__arrow::after {
      border-top-color: #f5f5f5 !important;
    }
  }

  &[x-placement^='right'] {
    .popper__arrow {
      border-right-color: #f5f5f5 !important;
    }

    .popper__arrow::after {
      border-right-color: #f5f5f5 !important;
    }
  }

  &[x-placement^='bottom'] {
    .popper__arrow {
      border-bottom-color: #f5f5f5 !important;
    }

    .popper__arrow::after {
      border-bottom-color: #f5f5f5 !important;
    }
  }

  &[x-placement^='left'] {
    .popper__arrow {
      border-left-color: #f5f5f5 !important;
    }

    .popper__arrow::after {
      border-left-color: #f5f5f5 !important;
    }
  }
}

.main-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  background-color: #fff;
  // border-radius: 15px;
}

.module-title {
  color: #191919;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  &::before {
    content: ' ';
    background-image: url(~@/assets/img/system/title-icon.png);
    display: inline-block;
    height: 24px;
    margin-right: 5px;
    vertical-align: middle;
    width: 24px;
  }
}
.sub-title {
  color: #191919;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  background: #edf5fe;
  border-radius: 8px;
  padding: 8px 15px;
  &::before {
    content: ' ';
    background-image: url(~@/assets/img/system/sub-title-icon.png);
    background-size: 100% 100%;
    display: inline-block;
    height: 24px;
    margin-right: 5px;
    vertical-align: middle;
    width: 24px;
  }
}

.rounded-corner-box {
  background-color: #fff;
  border-radius: 15px;
  padding: 24px;
}

.empty {
  width: 100%;
  height: 100%;
  color: #555;
  display: flex;
  justify-content: center;
  align-items: center;
}

.module-title {
  color: #191919;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  font-family: PingFangSC, PingFang SC;
  &::before {
    content: ' ';
    background-image: url(~@/assets/img/system/title-icon.png);
    display: inline-block;
    height: 24px;
    margin-right: 5px;
    vertical-align: middle;
    width: 24px;
  }
}
