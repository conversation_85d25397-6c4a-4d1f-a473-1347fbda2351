/*
 * @Author: your name
 * @Date: 2023-03-14 15:04:26
 * @LastEditTime: 2023-03-16 14:46:38
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \custom-app-plus\src\modules\ImportDataPage\api.js
 */
import { request } from '@/fawkes'
import { IResponse } from '@/types'

export interface IParams {
  formKey: string
}
// 表单模板下载
export function downloadTemplate(params: IParams) {
  return request<Blob>({
    url: '/sys-form/form/excel/template',
    method: 'post',
    data: params,
    responseType: 'blob'
  })
}

export interface IQueryData {
  fileToken: string
  page: number
  size: number
}

export interface IResponseListData {
  endRow: number
  hasNextPage: boolean
  hasPreviousPage: boolean
  isFirstPage: boolean
  isLastPage: boolean
  list: object[]
  navigateFirstPage: number
  navigateLastPage: number
  navigatePages: number
  navigatepageNums: number[]
  nextPage: number
  pageNum: number
  pageSize: number
  pages: number
  prePage: number
  size: number
  startRow: number
  total: number
}
// 查询上传的数据
export function queryList(params: IQueryData) {
  return request<IResponse<IResponseListData>>({
    url: '/sys-form/form/excel/query',
    method: 'post',
    params: params
  })
}

// 表格的列数据
export interface IFormColumn {
  activeFlag: boolean
  id: string
  name: string
  tagAttribute: null | string
}
// 获取表格的数据
export function queryFormElementList(params: IParams) {
  return request<IResponse<IFormColumn[]>>({
    url: '/sys-form/form/field',
    method: 'get',
    params
  })
}

export interface IImportMapParmas {
  formKey: string
  fileToken: string
  mapping: object
}
interface IImportDetailMapParmas extends IImportMapParmas {
  formKey: string
  fileToken: string
  mapping: object
  fieldName: string
}
export interface IImportMapData {
  failureCount: string
  successCount: string
  total: string
  fileToken?: string
}
export async function importMap(params: IImportMapParmas) {
  const res = await request<IResponse<IImportMapData>>({
    url: '/sys-form/form/excel/data',
    method: 'post',
    data: params
  })
  return res.data
}
export async function importDeatailMap(params: IImportDetailMapParmas) {
  const res = await request<IResponse<IImportMapData>>({
    url: '/sys-form/form/excel/detail/data',
    method: 'post',
    data: params
  })
  return res.data
}
//导出明细表数据
export interface IExportDataParam {
  bizId: string
  field: string
  formKey: string
}
export function exportDetailTableData(params: IExportDataParam) {
  return request({
    url: 'sys-form/form/detailTable/export',
    method: 'get',
    params: params,
    responseType: 'blob'
  })
}
