/*
 * @Author: <EMAIL>
 * @Date: 2019-11-05 14:33:45
 * @LastEditors: zhong_m
 * @LastEditTime: 2022-10-09 09:07:33
 * @Description: 侧边栏变量
 */
// 主题
$theme: #409eff; ///#409EFF;
// sidebar
$menuBg: #fff;
$menuHover: rgba($theme, 0.6);
$menuActive: #dbe2ef;

$subMenuBg: #fff;
$subMenuHover: rgba($theme, 0.6);

$menuItemHeight: 40px;

$sideBarWidth: 249px;
$collapseBarWith: 54px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  theme: $theme;
  menubg: $menuBg;
  menuhover: $menuHover;
  menuactive: $menuActive;
  submenubg: $subMenuBg;
  submenuhover: $subMenuHover;
  sidebarwidth: $sideBarWidth;
  collapsebarwith: $collapseBarWith;
  menuitemheight: $menuItemHeight;
}
