/*
 * @Author: your name
 * @Date: 2023-03-10 11:21:16
 * @LastEditTime: 2023-03-20 09:48:29
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \custom-app-plus\src\api\sys-form\application.ts
 */
import { request } from '@/fawkes'
import type { IResponse } from '@/types/'
import type { IParams } from '@/api/sys-form/app-management'

// 获取应用的模块数据请求参数
export interface IQueryModuleListData {
  groupId: string
  moduleName?: string
}
// 应用模块项
export interface IModuleListItem {
  activeDashboardId: string
  createBy: string
  createDate: string
  draftFlag: boolean
  formKey: string
  formName: string
  formType: string
  icon: null | string
  id: string
  menuId: string
  moduleDesc: null | string
  moduleName: string
  moduleStatus: string
  moduleType: string
  roleId: string
  sort: string
  updateBy: string
  updateDate: string
  onRelease?: boolean
  checked?: boolean
  modelKey?: string
}
// 获取应用下的模块列表
export async function queryModuleList(params: IQueryModuleListData) {
  const { data } = await request<IResponse<IModuleListItem[]>>({
    url: '/sys-form/form/group/card',
    method: 'get',
    params: params
  })
  const list = data.map((item) => {
    item.onRelease = item.moduleStatus === 'release'
    item.checked = false
    return item
  })
  return {
    list
  }
}

// 获取发布应用所需的选择数组
export interface IAppOptionsResponse {
  areaList: string[]
  groupIcon: string
  groupName: string
  tagList: ITagItem[]
  themeColor: string
}
// 发布应用选择的 tag
export interface ITagItem {
  config: {
    backColor: string
    borderColor: string
    textColor: string
  }
  tag: string
}
// 获取上架应用类型数组
export function queryAppOptions(groupId: string) {
  return request<IResponse<IAppOptionsResponse>>({
    url: '/sys-form/store/upload/option',
    method: 'GET',
    params: {
      groupId
    }
  })
}

// 获取 应用基本信息返回的 data
export interface IQueryAppInfoResponse {
  adminFullName: string
  adminName: string
  attachment: null
  createBy: string
  createByFullName: string
  createDate: string
  deleteFlag: number
  description: string
  draftStatus: string
  groupIcon: string
  groupName: string
  id: string
  menuId: string
  portalId: string
  sceneColor: string
  sort: number
  updateBy: string
  updateDate: string
  uploadStatus: null
}
//查询单个应用信息
export function queryAppInfo(id: string) {
  return request<IResponse<IQueryAppInfoResponse>>({
    url: '/sys-form/form/group',
    method: 'get',
    params: {
      id
    }
  })
}

// 删除当前应用请求数据
export interface IDeleteApp {
  id: string
  confirmInput: string
}
// 根据删除信息删除应用
export function deleteApp(data: IDeleteApp) {
  return request<IResponse<null | string[]>>({
    url: '/sys-form/form/group',
    method: 'delete',
    params: data
  })
}

export type IReleaseData = {
  id: string
  type: 'dashboard' | 'module'
}
export function onReleaseModule(params: IReleaseData) {
  return request({
    url: '/sys-form/form/onRelease',
    method: 'post',
    params: params
  })
}
// 对模块排序
export interface IModuleSort {
  id: string
  type: string
  sort: number
}
// 对模块进行排序
export function sortModule(data: IModuleSort[], params: IParams) {
  return request<IResponse<null>>({
    url: '/sys-form/form/sort',
    method: 'put',
    params,
    data
  })
}

// 导出模块信息
export function exportModule(id: string) {
  return request<IResponse<IQueryAppInfoResponse>>({
    url: '/sys-form/export/local/form',
    method: 'get',
    params: {
      id
    }
  })
}

// 一键发布或停用请求数据
export interface IPulishOrPause {
  action: string
  groupId: string
}
// 一键发布所有模块信息
export function pubishAllModule(data: IPulishOrPause) {
  return request<IResponse<null>>({
    url: '/sys-form/form/group/releaseOrPauseAllModule',
    method: 'post',
    params: data
  })
}

// 复制模块所需的数据
export interface ICopyModuleData {
  groupId: string
  formKey?: string
  moduleName?: string
  dashboardId?: string
  dashboardName?: string
}
// 复制模块设计信息
export function copyMoule(data: ICopyModuleData) {
  return request<IResponse<null>>({
    url: '/sys-form/module/copy',
    method: 'post',
    params: data
  })
}
// 复制仪表盘设计信息
export function copyDashboard(data: ICopyModuleData) {
  return request<IResponse<null>>({
    url: '/sys-form/form/dashboard/copy',
    method: 'get',
    params: data
  })
}

export interface IPauseParams {
  id: string
  type: string
}
// 停用-菜单
export function pauseModule(data: string, params: IPauseParams) {
  return request<IResponse<null | string[]>>({
    url: '/sys-form/form/pause',
    method: 'delete',
    params: params,
    data: data
  })
}

// 发布应用请求数据
export interface IOnReleaseAppData {
  admin: string
  id: string
  menuId: string
  sort: number
  sysMenu: {
    code: string
    component: string
    enable: boolean
    icon: string
    isAuth: boolean
    meta: string
    path: string
    sort: number
    title: string
    type: string
    id: string
    isGeneral: number
  }
}
// 启用--菜单-应用
export function onReleaseApp(data: IOnReleaseAppData) {
  return request<IResponse<null>>({
    url: '/sys-form/form/group/onRelease',
    method: 'post',
    data
  })
}

// 删除模块数据
export interface IDeleteModule {
  id?: string
  confirmInput: string
  formKey?: string
}
// 删除仪表
export function deleteDashboard(data: IDeleteModule) {
  return request<IResponse<null>>({
    url: '/sys-form/form/chart/dashboard',
    method: 'delete',
    params: data
  })
}
// 删除模块
export function deleteModule(data: IDeleteModule) {
  return request<IResponse<null | string[]>>({
    url: '/sys-form/form',
    method: 'delete',
    params: data
  })
}

// 发布应用的 form
export interface IPublishForm {
  themeColor: string
  storeIcon: string
  name: string
  industrialArea: string
  hashtagList: string[]
  brief: string
  storeDesc: string
  attachment: string
  imgList: string
}
// 上架应用
export function publish(data: IPublishForm) {
  return request({
    url: '/sys-form/store/upload',
    method: 'POST',
    data
  })
}
