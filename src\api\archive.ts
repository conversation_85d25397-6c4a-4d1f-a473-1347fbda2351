/*
 * @Date: 2022-09-29 11:14:16
 * @LastEditors: LSY
 * @LastEditTime: 2022-09-29 11:48:43
 * @Description: 档案相关
 * @FilePath: /flvue/src/api/archive.js
 */
import request from '@/utils/request'

/**
 * @description: 获取档案配置规则详情
 * @param {*} relatedId YES	模板id或者目录id
 * @return {*} source YES	01-模板 02-目录
 */
export function getArchiveRule(params) {
  return request({
    url: '/ym-archives/rule/config/getArchiveRule',
    method: 'get',
    params
  })
}