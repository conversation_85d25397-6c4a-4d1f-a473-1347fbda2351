import { request } from '@/fawkes'
import { IButton, IPageResponse, IResponse, IAddiConfig } from '@/types'
import { IClientInfo, IClientInfoOrigin } from '@/types/config'
import { getOwnLimitClientConfig } from '@/fawkes/saas/clientLimit'
import { AxiosResponse } from 'axios'
import { getSaasLimitClientConfigInMain } from '@/fawkes/saas/clientLimit'
import { IRegisterParam } from '@/api/sys-user'
export interface ILanguages {
  [props: string]: {
    [props: string]: string
  }
}
interface IExtraData {
  enableProxy: boolean
  source: string
  clientName: string
  ignoreCheckState: boolean
  clientId: string
  clientSecret: string
  redirectUri: string
  stackOverflowKey: string
  proxyPort: string
  unionId: boolean
  proxyAddr: string
  agentId: string
  alipayPublicKey: string
  codingCroupName: string
}
/**
 * @description: 获取语言资源
 * @param {*}
 * @return {*}
 */
//
export async function queryLang(langCode: string) {
  return (
    await request<IResponse<ILanguages>>({
      url: '/sys-system/lang/detail/name',
      method: 'GET',
      params: {
        langCode
      }
    })
  ).data
}

/**
 * @description: 获取应用配置
 * @param {*}
 * @return {*}
 */
export async function queryClientConfig() {
  const res = await request<IResponse<IClientInfoOrigin>>({
    url: '/sys-system/clientInfo',
    method: 'GET'
  })
  return res.data
}
/**
 * 获取各租户应用配置
 * @returns
 */
export function queryUserClients() {
  return request<IResponse<IClientInfoOrigin[]>>({
    url: '/sys-system/user/clients',
    method: 'get'
  })
}
/**
 * 设置应用配置
 * @param {*} data
 * @returns
 */
export function updateClientConfig(data: IClientInfo) {
  const limitData = getOwnLimitClientConfig(data)
  return request<IResponse<null>>({
    url: '/sys-system/client',
    method: 'put',
    data: limitData
  })
}
export function queryClientCatch(data = {}) {
  return request<IResponse<null>>({
    url: '/sys-system/clients/cache/sync',
    params: data,
    method: 'get'
  })
}
export function queryDictionaryCache(data = {}) {
  return request<IResponse<null>>({
    url: '/sys-system/dictionary/cache/sync',
    params: data,
    method: 'get'
  })
}

/** 获取按钮列表 */
export function queryButtons(params?: { roleId?: string; type?: string }) {
  return request<IResponse<IButton[]>>({
    url: '/sys-system/buttons',
    method: 'get',
    params: params
  })
}
export type IEnum = {
  [props: string]: { code: string; [props: string]: string }[]
}
/** 按类型获取枚举 */
export function queryEnum(data: string) {
  return request<IResponse<IEnum>>({
    url: '/sys-system/dictionary/detail/list',
    method: 'get',
    params: { code: data }
  })
}
interface IFileConfig {
  enabled?: boolean
  fileTypeWhiteList: string
  maxFileSize: number
  maxRequestSize: number
}
//获取文件上传配置
export function queryFileUploadConfig() {
  return request<IResponse<IFileConfig>>({
    url: '/sys-system/fileUploadConfig',
    method: 'get'
  })
}
//更新文件上传配置信息
export function updateFileUploadConfig(data: IFileConfig) {
  return request<IResponse<null>>({
    url: '/sys-system/fileUploadConfig',
    method: 'put',
    data: data
  })
}

interface IBaseConfig {
  appKey?: string
  appSecret: string
  id: string
  proxyConfig: { hostName: null | string; port: null | string; scheme: null | string }
  serverName: string
  state: boolean
}

interface IDingTalkServerConfig extends IBaseConfig {
  agentId: string
}

interface ILarkServerConfig extends IBaseConfig {
  appId: string
}

interface IMailServerConfig {
  defaultEncoding: string
  host: string
  id: string
  password: null | string
  port: number | null
  properties: null | object
  serverName: string
  state: boolean
  username: string | null
}

interface IQywxServerConfig {
  agentId: string
  appSecret: string
  corpId: string
  host: string | null
  id: string
  proxyConfig: { hostName: null | string; port: null | number; scheme: null | string }
  serverName: string
  state: boolean
}

interface ISmsServerConfig {
  channelNumber: null | number | string
  host: string
  id: string
  password: null | string
  port: null | number
  proxyConfig: { hostName: null | string; port: null | number; scheme: null | string }
  serverName: string
  sign: null | string
  state: boolean
  type: string
  username: string
}
export interface IServerConfigData {
  dingTalkServerConfig: IDingTalkServerConfig[]
  fileServerConfig: object
  jpushServerConfig: IBaseConfig[]
  larkServerConfig: ILarkServerConfig[]
  mailServerConfig: IMailServerConfig[]
  qywxServerConfig: IQywxServerConfig[]
  smsServerConfig: ISmsServerConfig[]
}

/** 获取配置信息 */
export function queryServerConfig() {
  return request<IResponse<IServerConfigData>>({
    url: '/sys-system/serverConfig',
    method: 'get'
  })
}

export interface IServerConfig {
  proxyConfig?: {
    hostName: string | null
    port: number | null
    scheme: string | null
  }
  host?: string
  password?: string
  port?: number
  username?: string
  serverName?: string
  sign?: string
  type?: string
  state?: boolean
  agentId?: string
  appSecret?: string
  appKey?: string
  appId?: string
  corpId?: string
  value?: string
  properties?: {
    mail?: {
      smtp?: {
        proxy?: {
          host?: string | null
          port?: string | null
        }
      }
    }
  }
  id?: string
}
interface IDingTalkServer {
  agentId: string
  appKey: string
  appSecret: string
  id: string
}
export interface IFileServer {
  accessKey: string
  bucketName: string
  endpoint: string
  endpointKey: string
  location: string
  ossType: string
  secretKey: string
  serverName: string
  state: boolean
}
interface IJpushLarkServer {
  appKey: string
  appSecret: string
  id: string
  proxyConfig: { hostName: string; port: string; scheme: string }
  serverName: string
  state: boolean
}
interface IMailServer {
  defaultEncoding: string
  host: string
  id: string
  password: string
  port: number
  properties: string
  serverName: string
  state: boolean
  username: string
}
interface IQywxServer {
  agentId: string
  appSecret: string
  corpId: string
  host: string
  id: string
  proxyConfig: { hostName: string; port: string; scheme: string }
  serverName: string
  state: boolean
}
interface ISmsServer {
  channelNumber: string
  host: string
  id: string
  password: string
  port: 80
  proxyConfig: { hostName: string; port: string; scheme: string }
  serverName: string
  sign: string
  state: boolean
  type: string
  username: string
}
export interface IFileServerVConfig {
  dingTalkServerConfig: IDingTalkServer[]
  fileServerConfig: {
    client: { fawkes: IFileServer[] }
  }
  jpushServerConfig: IJpushLarkServer[]
  larkServerConfig: IJpushLarkServer[]
  mailServerConfig: IMailServer[]
  qywxServerConfig: IQywxServer[]
  smsServerConfig: ISmsServer[]
}
/** 更新配置信息 */
export function updateServerConfig(data: IServerConfigData) {
  return request<IResponse>({
    url: '/sys-system/serverConfig',
    method: 'put',
    data: data
  })
}
//获取第三方认证客户端列表
export function quertExtraData() {
  return request<IResponse>({
    url: '/sys-system/oauth/clients',
    method: 'get'
  })
}
//更新第三方应用信息
export function updateExtraData(data: IExtraData) {
  return request<IResponse<null>>({
    url: '/sys-system/oauth/client',
    method: 'put',
    data: data
  })
}
//新增第三方应用
export function addExtraData(data: IExtraData) {
  return request<IResponse<null>>({
    url: '/sys-system/oauth/client',
    method: 'post',
    data: data
  })
}
interface IDicParams {
  serviceType: number
  langCode: string
  code: string
  page: number
  size: number
}
export interface IDicInfo {
  code: string
  content: string
  id: string
  isGeneral: true
  langCode: string
  parentId: string
  pathId: string
  portalId: null
  serviceType: number
  sort: number
  tenantId: number
}
/**
 * @description: 查询字典列表-分页
 */
export function queryDic(data: IDicParams) {
  return request<IPageResponse<IDicInfo>>({
    url: '/sys-system/dictionary/page',
    method: 'get',
    params: data
  })
}
interface IDicDetailParams {
  parentId: string
  langCode: string
}
export interface IDicDetailInfo {
  id: string
  parentId: string
  content: IDicInfo
  children?: IDicDetailInfo
}
/**
 * @description: 查询字典详情列表
 * @param {pId} 字典主键
 */
export function queryDicDetail(data: IDicDetailParams) {
  return request<IResponse<IDicDetailInfo[]>>({
    url: '/sys-system/dictionary/child/list',
    method: 'get',
    params: data
  })
}
interface IAllDetailParams {
  dictionaryId: string
}
export interface IDicDetailLang {
  content: IDicInfo
  dictionaryId: string
  id: string
  langCode: string
  tenantId: number
}
//获取指定code的所有语言翻译
export function queryAllDicDetail(data: IAllDetailParams) {
  return request<IResponse<IDicDetailLang[]>>({
    url: '/sys-system/dictionary/detail',
    method: 'get',
    params: data
  })
}
export interface IAddEditDic {
  code: string
  content: string
  id?: string
  langCode: string
  parentId: string
  pathId: string
}
/**
 * @description: 新增字典
 */
export function addDic(data: IAddEditDic[]) {
  return request<IResponse<null>>({
    url: '/sys-system/dictionary',
    method: 'post',
    data: data
  })
}
/**
 * @description: 编辑字典
 */
export function updateDic(data: IAddEditDic[]) {
  return request<IResponse<null>>({
    url: '/sys-system/dictionary',
    method: 'put',
    data: data
  })
}
interface IDeleteDic {
  id: string
}
/**
 * @description: 删除字典
 */
export function deleteDic(id: string) {
  return request<IResponse<null>>({
    url: '/sys-system/dictionary',
    method: 'delete',
    params: { id }
  })
}
export interface IAddUpdateDetail {
  code: string
  content: string
  langCode: string
  parentId: string
  pathId: string
  serviceType: number
  sort: number
  id?: string
}
/**
 * @description: 保存、更新字典详情列表（全量跟新）
 */
export function addDicDetail(data: IAddUpdateDetail[]) {
  return request<IResponse<null>>({
    url: '/sys-system/dictionary',
    method: 'post',
    data: data
  })
}
export function updateDicDetail(data: IAddUpdateDetail[]) {
  return request<IResponse<null>>({
    url: '/sys-system/dictionary',
    method: 'put',
    data: data
  })
}
/**
 * @description: 删除字典详情
 * @param {id} 字典详情id
 */
export function deleteDicDetail(params: IDeleteDic) {
  return request<IResponse<null>>({
    url: '/sys-system/dictionary/detail',
    method: 'delete',
    params: params
  })
}

/**字典导出 */
export function exportDic(data: IDicInfo[]) {
  return request<AxiosResponse<Blob>>({
    url: '/sys-system/dictionary/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
/**字典导入 */
export function importDic(data: FormData) {
  return request<IResponse<null>>({
    url: '/sys-system/dictionary/import',
    method: 'post',
    data: data
  })
}
/** 根据id获取应用详情 */
export function queryClient() {
  return request<IResponse<IClientInfo[]>>({
    url: '/sys-system/clients',
    method: 'get'
  })
}
interface ITenant {
  id: string
  tenantId: number
  tenantName: string
}
export function getTenantList(params: ITenantPageParams) {
  return request<IPageResponse<ITenant>>({
    url: '/sys-system/tenant/page',
    method: 'get',
    params: params
  })
}
interface IClientPageParams {
  pageNo: number
  pageSize: number
  sort: string
  columnName: string
  tenantId: number | string
}
//获取应用分页
export function queryClientPage(data: IClientPageParams) {
  return request<IPageResponse<IClientInfoOrigin>>({
    url: '/sys-system/clients/page',
    method: 'get',
    params: data
  })
}
/** 删除应用 */
export function deleteClient(id: string) {
  return request<IResponse<null>>({
    url: '/sys-system/client',
    method: 'DELETE',
    params: {
      id
    }
  })
}
interface IAddClietn {
  clientId: string
  clientName: string
  clientSecret: string
  id?: string
  webServerRedirectUri: string
  initTenantData?: boolean
}
/** 新建租户下应用 */
export function addClient(data: IAddClietn) {
  const limitData = getSaasLimitClientConfigInMain(data)
  return request<IResponse<IClientInfoOrigin>>({
    url: '/sys-system/client',
    method: 'post',
    data: limitData
  })
}
/**
 * @description: 更新应用配置，非fawkes应用时只能进行有限配置更新
 * @param {*} data
 */
export function updateClient(data: IAddClietn) {
  const limitData = getSaasLimitClientConfigInMain(data)
  return request<IResponse<null>>({
    url: '/sys-system/client',
    method: 'PUT',
    data: limitData,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
interface ITenantPageParams {
  pageNo: number
  pageSize: number
  sort?: string
  columnName?: string
}

export interface ITenantPageInfo {
  client: IClientInfoOrigin
  id: string
  tenantId: number
  tenantName: string
}
//获得所有租户分页saas版本
export function queryTenantInSaas(data: ITenantPageParams) {
  return request<IPageResponse<ITenantPageInfo>>({
    url: '/sys-system/saas/tenant/page',
    method: 'get',
    params: data
  })
}
//删除租户 根据id
export function deleteTenant(id: string) {
  return request<IResponse<null>>({
    url: '/sys-system/tenant',
    method: 'delete',
    params: {
      id
    }
  })
}
interface IAddTenantData {
  additionalInformation: IAddiConfig
  adminUserName: string
  clientName: string
  remark: string
  initMenuIdList: string[]
}
interface ITenantInfo {
  accessTokenValidity: number
  additionalInformation: string
  adminUserId: string
  adminUserName: string
  adminUserPassword: string
  authorities: string
  authorizedGrantTypes: string
  autoapprove: string
  clientId: string
  clientName: string
  clientSecret: string
  id: string
  initMenuIdList: string
  isDefault: true
  parantId: string
  pathId: string
  refreshTokenValidity: number
  remark: string
  resourceIds: string
  scope: string
  sort: number
  tenantId: number
  tenantName: string
  webServerRedirectUri: null
}
export function addTenant(data: IAddTenantData) {
  const limitData = getSaasLimitClientConfigInMain(data)
  return request<IResponse<ITenantInfo>>({
    url: '/sys-system/tenant/init',
    method: 'post',
    data: limitData,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
interface ITreeParams {
  portalId: string
  type: string | number
}
export interface IMenuInfo {
  code: string
  component: string
  enable: boolean
  hidden: boolean
  icon: string
  id: string
  isAuth: boolean
  isGeneral: boolean
  meta: string
  parentId: string
  path: string
  pathId: string
  portalId: string
  redirect: string
  remark: string
  sort: number
  tenantId: number
  title: string
  type: number
}
export interface IMenuTreeData {
  content: IMenuInfo
  id: string
  parentId: string
  children?: IMenuTreeData[]
}
/** 获取菜单树 */
export function queryMenusTree(params: ITreeParams) {
  return request<IResponse<IMenuTreeData[]>>({
    url: '/sys-system/menus/tree',
    method: 'get',
    params: params
  })
}
//编辑租户
export function updateTenant(data: ITenantData) {
  return request<IResponse<null>>({
    url: '/sys-system/tenant',
    method: 'put',
    data: data
  })
}
interface ITenantData {
  id: string
  tenantId: number
  tenantName: string
}
export function queryTenant(id: string) {
  return request<IResponse<ITenantData>>({
    url: '/sys-system/tenant',
    method: 'get',
    params: {
      id
    }
  })
}
export function queryClientInfo(id: string) {
  return request<IResponse<IClientInfo>>({
    url: '/sys-system/client',
    method: 'get',
    params: {
      id
    }
  })
}
interface ITenantConfig {
  clientId: string
  tenantId: number
}
interface IRoleInfo {
  code: string
  hidden: false
  id: string
  isGeneral: false
  name: string
  parentId: string
  pathId: string
  portalId: string
  remark: string
  sort: number
  tenantId: number
  type: number
}
export function queryRoleInOtherTenantByCode(code: string, tenantConfig: ITenantConfig) {
  return request<IResponse<IRoleInfo>>({
    url: '/sys-system/role',
    method: 'get',
    params: {
      code
    },
    tenantConfig
  })
}
interface IRoleUserPageParams {
  pageNo: number
  pageSize: number
  searchValue: string
  sort: string
  columnName: string
  roleId: string
}
export function queryRoleUsersInOtherTenant(params: IRoleUserPageParams, tenantConfig: ITenantConfig) {
  return request<IPageResponse<IRegisterParam>>({
    url: '/sys-user/role/users/page',
    method: 'get',
    params: {
      ...params
    },
    tenantConfig
  })
}
interface INotSaasTenant {
  initTenantData: boolean
  tenantName: string
}
//新增租户
export function addNotSaasTenant(data: INotSaasTenant) {
  return request<IResponse<ITenantData>>({
    url: '/sys-system/tenant',
    method: 'post',
    data: data
  })
}
//获得所有租户
export function queryAllTenant() {
  return request<IResponse<ITenantData[]>>({
    url: '/sys-system/tenants',
    method: 'get'
  })
}
