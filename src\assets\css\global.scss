// 清除浮动
.clearfix {
  & {
    *zoom: 1;
  }

  &:before,
  &:after {
    display: table;
    content: "";
  }

  &:after {
    clear: both;
  }
}

.hide {display: none!important;}

.align-center {
    display: flex;
    align-items: center;
}

.tl {text-align: left;}
.tr {text-align: right;}
.tc {text-align: center;}

.vb {vertical-align: bottom}

.fl {float: left; *display: inline;}
.fr {float: right; *display: inline;}

// 外边距
.mt5 {margin-top: 5px;}
.mt10 {margin-top: 10px;}
.mt15 {margin-top: 15px;}
.mt20 {margin-top: 20px;}
.mt30 {margin-top: 30px;}
.mt40 {margin-top: 40px;}
.mt50 {margin-top: 50px;}
.mt90 {margin-top: 90px;}
.mt100 {margin-top: 100px;}
.mt-20 {margin-top: -20px;}
.mt-30 {margin-top: -30px;}

.mr2 {margin-right: 2px;}
.mr4 {margin-right: 4px;}
.mr5 {margin-right: 5px;}
.mr10 {margin-right: 10px;}
.mr15 {margin-right: 15px;}
.mr20 {margin-right: 20px;}
.mr30 {margin-right: 30px;}
.mr50 {margin-right: 50px;}
.mr60 {margin-right: 60px;}

.mb5 {margin-bottom: 5px;}
.mb10 {margin-bottom: 10px;}
.mb15 {margin-bottom: 15px;}
.mb20 {margin-bottom: 20px;}
.mb30 {margin-bottom: 30px;}
.mb40 {margin-bottom: 40px;}

.ml2 {margin-left: 2px;}
.ml4 {margin-left: 4px;}
.ml5 {margin-left: 5px;}
.ml10 {margin-left: 10px;}
.ml15 {margin-left: 15px;}
.ml20 {margin-left: 20px;}
.ml30 {margin-left: 30px;}
.ml40 {margin-left: 40px;}

// 内边距
.pt0 {padding-top: 0px;}
.pt5 {padding-top: 5px;}
.pt10 {padding-top: 10px;}
.pt15 {padding-top: 15px;}
.pt20 {padding-top: 20px;}
.pt30 {padding-top: 30px;}
.pt40 {padding-top: 40px;}

.pr0 {padding-right: 0px;}
.pr5 {padding-right: 5px;}
.pr10 {padding-right: 10px;}
.pr15 {padding-right: 15px;}
.pr20 {padding-right: 20px;}
.pr30 {padding-right: 30px;}
.pr40 {padding-right: 40px;}

.pb0 {padding-bottom: 0px;}
.pb5 {padding-bottom: 5px;}
.pb10 {padding-bottom: 10px;}
.pb15 {padding-bottom: 15px;}
.pb20 {padding-bottom: 20px;}
.pb30 {padding-bottom: 30px;}
.pb40 {padding-bottom: 40px;}

.pl0 {padding-left: 0px;}
.pl5 {padding-left: 5px;}
.pl10 {padding-left: 10px;}
.pl15 {padding-left: 15px;}
.pl20 {padding-left: 20px;}
.pl30 {padding-left: 30px;}
.pl40 {padding-left: 40px;}

// 字体大小
.f12 {font-size: 12px;}
.f14 {font-size: 14px;}
.f16 {font-size: 16px;}
.f18 {font-size: 18px;}
.f20 {font-size: 20px;}
.f22 {font-size: 22px;}
.f24 {font-size: 24px;}
.f26 {font-size: 26px;}
.f40 {font-size: 40px;}
.f48 {font-size: 48px;}

// 一些固定宽度
.wauto {width: auto;}
.w40 {width: 40px;}
.w50 {width: 50px;}
.w60 {width: 60px;}
.w65 {width: 65px;}
.w70 {width: 70px;}
.w80 {width: 80px;}
.w90 {width: 90px;}
.w100 {width: 100px;}
.w110 {width: 110px;}
.w120 {width: 120px;}
.w130 {width: 130px;}
.w140 {width: 140px;}
.w150 {width: 150px;}
.w160 {width: 160px;}
.w180 {width: 180px;}
.w200 {width: 200px;}
.w220 {width: 220px;}
.w260 {width: 260px;}
.w300 {width: 300px;}
.w320 {width: 320px;}
.w350 {width: 350px;}
.pst10 {width: 10%;}
.pst12 {width: 12%;}
.pst14 {width: 14%;}
.pst15 {width: 15%;}
.pst16 {width: 16%;}
.pst18 {width: 18%;}
.pst20 {width: 20%;}
.pst30 {width: 30%;}
.pst40 {width: 40%;}
.pst50 {width: 50%;}
.pst60 {width: 60%;}
.pst70 {width: 70%;}
.pst80 {width: 80%;}
.pst90 {width: 90%;}
.pst100 {width: 100%;}

.bold {
  font-weight: bold;
}
.underline {
  text-decoration: underline;
}

// 溢出显示点点点
.overflow_dotted {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}