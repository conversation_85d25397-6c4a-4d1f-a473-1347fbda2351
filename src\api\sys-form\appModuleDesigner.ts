import { request } from '@/fawkes'
import type { IResponse } from '@/types'
import { IModule, ITableBtn, IDetailTemplate } from './appModuleDesigner.type'
export function postModule(data: IModule) {
  return request({
    url: '/sys-form/form/save',
    method: 'post',
    data: data
  })
}
export function queryModule(params) {
  return request<IResponse<IModule>>({
    url: '/sys-form/form/latest',
    method: 'get',
    params: params
  })
}
export type IReleaseData = {
  id: string
  type: 'dashboard' | 'module'
}
export function postReleaseModule(params: IReleaseData) {
  return request({
    url: '/sys-form/form/onRelease',
    method: 'post',
    params: params
  })
}

export interface IConnectorParams {
  bizIds?: string
  bizId?: string
  formKey: string
}

export function connectorHead(params: IConnectorParams, data: ITableBtn) {
  return request({
    url: '/sys-form/connector/form/head',
    method: 'post',
    params,
    data
  })
}

export function connectorInner(params: IConnectorParams, data: ITableBtn) {
  return request({
    url: '/sys-form/connector/form/inner',
    method: 'post',
    params,
    data
  })
}

export function downTempalte(data: IDetailTemplate) {
  return request({
    url: '/sys-form/form/excel/template',
    method: 'post',
    responseType: 'blob',
    data
  })
}
