/*
 * @Author: your name
 * @Date: 2023-03-10 10:21:53
 * @LastEditTime: 2023-03-20 09:48:18
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \custom-app-plus\src\api\sys-form\app-management.ts
 */
import { request } from '@/fawkes'
import type { IResponse } from '@/types/'

/**
 * @description: 根据应用名称和门户Id查询应用列表
 */
// 获取当前应用基本信息请求数据
export interface IQueryAppData {
  groupName: string // 模糊查询应用名称
}
// 应用的基本信息
export interface IAppItem {
  adminFullName: string
  adminName: string
  attachment: null | string
  createBy: string
  createByFullName: string
  createDate: string
  deleteFlag: number
  description: string
  draftStatus: string
  groupIcon: string
  groupName: string
  id: string
  menuId: string
  sceneColor: string
  sort: number
  updateBy: string
  updateDate: string
  uploadStatus: null | string
  checked?: boolean
  viewUserName?: string
  viewUserFullName?: string
  viewDeptNo?: string
  viewDeptName?: string
}
// 获取应用列表数据
export async function queryAppList(data: IQueryAppData) {
  const res = await request<IResponse<IAppItem[]>>({
    url: '/sys-form/form/group/list',
    method: 'get',
    params: data
  })
  const sortArr: number[] = []
  const list = res.data.map((item) => {
    item.checked = false
    item.sort ? sortArr.push(item.sort) : ''
    return item
  })
  return {
    list,
    sortArr
  }
}

// 导入应用返回的接口数据
export interface IImportResPonseData {
  adminFullName: string
  adminName: string
  attachment: null | string
  createBy: string
  createByFullName: string
  createDate: string
  deleteFlag: number
  description: string
  draftStatus: string
  groupIcon: string
  groupName: string
  id: string
  menuId: null | string
  portalId: string
  sceneColor: string
  sort: number
  updateBy: string
  updateDate: string
  uploadStatus: null | string
}
// 导入外部应用
export function importApp(data: FormData) {
  return request<IResponse<IImportResPonseData>>({
    url: '/sys-form/import/local/group',
    method: 'post',
    data
  })
}

// 发布应用数据
export interface IOnReleaseAppItem {
  admin: string
  id: string
  menuId: string
  sort: string | number
  sysMenu: ISysMenu
}
export interface ISysMenu {
  code: string
  component: string
  enable: boolean
  icon: string
  isAuth: boolean
  meta: string
  path: string
  sort: number
  title: string
  id: string
  isGeneral: number
}
// 启用--菜单-应用
export function onReleaseApp(data: IOnReleaseAppItem) {
  return request<IResponse<null>>({
    url: '/sys-form/form/group/onRelease',
    method: 'post',
    data
  })
}

// 用于排序数据
export interface ISortData {
  id: string
  sort: number
}
export interface IParams {
  type: string
}

// 对应用管理上面的应用进行排序
export function sortApp(data: ISortData[], params: IParams) {
  return request<IResponse<null>>({
    url: '/sys-form/form/sort',
    method: 'put',
    params,
    data
  })
}

//   导出应用
export function exportApp(data: string[]) {
  return request<IResponse<null>>({
    url: '/sys-form/export/local/group/zip',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 复制应用数据
export interface ICopyAppData {
  groupId: string
  groupName: string
}
// 复制应用
export function copyApp(data: ICopyAppData) {
  return request<IResponse<IImportResPonseData>>({
    url: `/sys-form/group/copy`,
    method: 'post',
    params: data
  })
}

// 新增应用数据
export interface IAddAppData {
  description: string
  draftStatus: string
  sceneColor: string
  groupIcon: string
  groupName: string
  id: string
  adminName: string
  adminFullName: string
  sort: number | string
  sysMenu?: ISysMenu
}
// 新增应用接口返回的数据
export interface IAddAppResponseData {
  adminFullName: string
  adminName: string
  attachment: null | string
  createBy: null | string
  createByFullName: null | string
  createDate: null | string
  deleteFlag: null | string
  description: string
  draftStatus: string
  groupIcon: string
  groupName: string
  id: string
  menuId: null | string
  portalId: string
  sceneColor: string
  sort: number
  updateBy: string
  updateDate: string
  uploadStatus: null | string
}
// 新增应用
export function addApp(data: IAddAppData, method: string) {
  return request<IResponse<IResponse<IAddAppResponseData>>>({
    url: '/sys-form/form/group',
    method,
    data
  })
}

// 删除应用传入 id 和 name
export interface IDeleteData {
  id: string
  name: string
}
// 删除应用
export function deleteApp(data: IDeleteData) {
  return request<IResponse<null | string[]>>({
    url: `/sys-form/form/group`,
    method: 'delete',
    params: {
      confirmInput: data.name,
      id: data.id
    }
  })
}
