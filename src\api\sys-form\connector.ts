import { request } from '@/fawkes'
import type { IResponse } from '@/types/'
import { IParamItem } from '@/modules/WorkPackageConfig/ConnectorDesign/type'

export interface IConnectorParam {
  inputParams?: IParamItem[]
  outputParams?: IParamItem[]
}

//连接器类型
//新增连接器类型 1-工作包节点 2-关联数据 3-自定义按钮 4-下拉选择 5-级联选择 6-流程设计节点
export type IConnectorType = '1' | '2' | '3' | '4' | '5' | '6' | ''

export interface IConnector {
  checked?: boolean
  authMode?: null | '0' | '1' | '2' // 这里的 any 表示 authMode 可以是任意类型，因为提供的信息太少无法推断其类型
  baseUrl: string
  connectorName: string
  description: string
  domain: string
  finalUrl: string
  icon: string
  iconColor: string
  id?: string
  method: string
  protocol: string
  requestBody: string
  returnBody: string
  tenantId?: number
  createDate?: string
  updateDate?: string
  connectorType?: IConnectorType //新增连接器类型 1-工作包节点 2-关联数据 3-自定义按钮 4-目录树
}

export interface IConnectorSubmitParam extends IConnectorParam {
  connectorInfo: IConnector
}

//查询连接器param配置
export function queryConnectorParam(params: { connectorId: string }) {
  return request<IResponse<IConnectorParam>>({
    url: '/sys-form/connectorParam',
    method: 'get',
    params
  })
}

// 查询连接器列表
export function queryConnectorList(params?: { name: string; connectorType: '1' | '2' | '3' | '4' | '' }) {
  return request<IResponse<IConnector[]>>({
    url: '/sys-form/connector/list',
    method: 'get',
    params: {
      name: params.name,
      connectorType: params.connectorType
    }
  })
}

// 删除连接器
export function deleteConnector(id: string) {
  return request<IResponse<null>>({
    url: '/sys-form/connector',
    method: 'delete',
    params: {
      id
    }
  })
}

// 复制连接器
export function copyConnector(params: { id: string; name: string }) {
  return request<IResponse<IConnector>>({
    url: '/sys-form/connector/copy',
    method: 'put',
    params
  })
}

// 查询连接器详情
export function queryConnector(id: string) {
  return request<IResponse<IConnector>>({
    url: '/sys-form/connector',
    method: 'get',
    params: {
      id
    }
  })
}

// 新建连接器
export function createConnector(data: IConnectorSubmitParam) {
  return request<IResponse<IConnector>>({
    url: '/sys-form/connector/save',
    method: 'post',
    data: data
  })
}

// 编辑连接器
export function editConnector(data: IConnectorSubmitParam) {
  return request<IResponse<IConnector>>({
    url: '/sys-form/connector/edit',
    method: 'post',
    data: data
  })
}

interface ITestDataItem {
  connectorParamName: string
  value: any
  paramColumnType: string
  fieldName?: string
}
interface ITestConnectorParam {
  bodyData: ITestDataItem[]
  queryData: ITestDataItem[]
  headerData: ITestDataItem[]
  pathData: ITestDataItem[]
  connectorId: string
}

// 测试连接器
// 返回data为用户配置内容
export function testConnector(data: ITestConnectorParam) {
  return request<IResponse<any>>({
    url: '/sys-form/connector/test',
    method: 'post',
    data: {
      ...data
    }
  })
}

//获取连接器的执行结果
export function queryConnectorExecutionResult(data: { connectorId: string }) {
  return request({
    url: '/sys-form/connector/handle',
    method: 'post',
    data
  })
}

//关联连接器
export interface IRelateInputParamsItem extends IParamItem {
  showValue: string
  value: any //配置的真实值
  error?: string //在使用阶段用于校验
}
