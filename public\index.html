<!--
 * @Author: your name
 * @Date: 2021-07-08 15:10:59
 * @LastEditTime: 2022-07-07 16:18:44
 * @LastEditors: <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: \custom-app\public\index.html
-->
<!DOCTYPE html>
<html data-name="wp">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" id="titleIcon">
    <title>
        <%= webpackConfig.name %>
    </title>
</head>
<script src="<%= BASE_URL %>static/sm4/s4.js"></script>
<script src="<%= BASE_URL %>static/sm4/byte&string.js"></script>
<script src="<%= BASE_URL %>static/sm4/smutils.js"></script>
<script src="<%= BASE_URL %>static/js/decimal.min.js"></script>
<script>
    var ENV_SAAS_MODE = 'FKS_SAAS_MODE'
</script>
<style>
  .main-loading {
      position: fixed;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      background-color: var(--theme-color);
      transition: background-color .1s ease;
      z-index: 9999;
  }

  .main-loading::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      top: 0;
      left: 0;
      background: #ffffffee;
  }

  .main-loading .loading {
      transform: translate(-50%, -50%);
      top: 50%;
      left: 50%;
      position: absolute;
  }
</style>

  <body>
    <noscript>
      <strong
        >We're sorry but central-system doesn't work properly without JavaScript
        enabled. Please enable it to continue.</strong
      >
    </noscript>

    <div class="main-loading" id="main-loading">
      <img class="loading" src="<%= BASE_URL %>static/img/loading.gif" />
    </div>

    <div id="fks-app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
