/*
 * by: chen ming
 * time: 2019/12/2
 * desc: 文件相关
 * */
import { request } from '@/fawkes'
import { IResponse } from '@/types'
interface IFileData {
  link: string //文件url链接
  fileToken: string //文件token
  fileName: string //文件名
  extName: string //文件拓展名
  groupToken: string //文件组名
  dir: string //所在文件夹名
  size: string //文件大小
  version: string //文件版本
  objectName: string //存储服务器中的文件名
  resource: Blob //文件字节流
}
export function updateFile(data: { fileToken: string }) {
  return request<IResponse<IFileData>>({
    url: '/sys-signature/file',
    method: 'GET',
    params: data
  })
}
//上传文件 form data格式
export function uploadFile(data: { fileToken: string }) {
  return request<IResponse<IFileData>>({
    url: '/sys-signature/file',
    method: 'POST',
    params: data
  })
}
export function deleteFile(data: { fileToken: string }) {
  return request<IResponse<null>>({
    url: '/sys-signature/file',
    method: 'DELETE',
    params: data
  })
}
