.fks-user-transfer-dialog {
  .fks-user-transfer-container {
    display: flex;
    overflow: auto;
    >div {
      min-width: 340px;
    }
  }
}
.fks-query-page {
  min-height: 400px;
}
.mobile {
  @media screen and (max-width: 768px) {
    .main-wrapper {
      min-width: auto !important;
      &.monitor-container {
        >div {
          min-width: auto ;
        }
        .dashboard-box {
          width: calc( (100% - 30px) / 3 );
          height: 30vw;
          padding-bottom: 0;
          .dashboard {
            width: 100%;
            height: 100%;
          }
          .dash-title {
            margin-top: -24px;
          }
        }
        #cpu_line_chart,
        #mem_line_chart,
        #disk_line_chart {
          height: 30vw !important;
          min-height: 150px;
        }
      }
      &.custom-resizer {
        display: flex;
        flex-flow: row nowrap;
        overflow: auto;
        > div {
          flex: none;
          &:last-of-type {
            flex: 0 0 100%;
          }
        }
      }
      .fks-query-page {
        padding: 10px 16px;
      }

    }
    .personal-center-container {
      overflow: auto;
      .personal-center-detail-list {
        flex : 0 0 100%;
        >div {
          min-width: auto;
        }
      }
    }
    .container-both {
      flex-direction: column;
      >div {
        width: auto;
      }
    }
    .error {
      .wscn-http404 {
        .text {
          flex: 1 0 280px;
          min-width: auto;
          padding-left: 0;
        }
      }

    }
  }
  @media screen and (max-width: 480px) {
    .main-container {
      margin-left: 0 !important;
    }
    .collapseSidebar {
      .sidebar-container {
        display: none;
      }
      .logo-container {
        display: none;
      }
    }
    #fks-app .main-container .fks-app-main.tags-view.footer-view
    .fks-app-main {
      &.tags-view.footer-view {
        padding: 0 8px 40px;
      }
      > div {
        min-width: auto !important;
      }
      .fks-query-page {
        display: flex;
        flex-flow: column;
        padding: 10px 16px;
        .fks-query-single-search {
          display: flex;
          flex-flow: row wrap;
          justify-content: space-between;
          height: auto;
          .search-content {
            .single-searchBox {
              .single-searchBox-item {
                width: 100%;
                margin-right: 0;
              }
            }
          }
        }
        .fks-searchbar {
          .search-input {
            width: 100% !important;
          }
        }
        .fks-query-body {
          flex: 1;
          height: 0 !important;
        }
        .fks-query-pagebox {
          position: relative;
          bottom: 0;
          .fks-pagination {
            flex-flow: row wrap;
          }
        }
      }
      .main-wrapper {
        // display: flex;
        // flex-flow: column;
        .line-button {
          .page-button {
            flex-flow: column;
            .page-title {
              ~div {
                display: flex;
                flex-flow: row wrap;
                justify-content: flex-end;
              }
            }
          }

        }
        .fks-row {
          .fks-col {
            min-width: auto;
            .row-content {
              flex-flow: row wrap;
              height: auto;
            }
          }
        }
        .dashboard-box {
          width: calc( (100% - 30px) / 3 );
          height: 30vw;
          padding-bottom: 0;
          .dashboard {
            width: 100%;
            height: 100%;
          }
        }
      }

    }
    .fks-message {
      width: 80% !important;
      min-width: 200px !important;
      height: auto !important;
      .fks-message__content {
        word-break: break-all;
      }
    }
    .fks-form {
      .fks-form-item {
        width: 100% !important;
      }
    }
    .editor {
      #div2 {
        width: 100%;
      }
      #div3 {
        display: none;
      }
    }
  }
  // @media screen and (min-width: 481px) and (max-width: 768px) {
  //   .main-wrapper {

  //   }
  // }
}
