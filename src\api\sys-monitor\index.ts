import { request } from '@/fawkes'
import { IPageResponse, IResponse } from '@/types'

interface IMonitorPage extends IMonitor {
  dur: number //页面停留时间
  fc: string //前向模块编码
  fn: string //前向模块名称
  fpa: string //前向模块路径
  fun: string //‘页面切换’
}
interface IMonitor {
  lan: string //浏览器语言
  mc: string //模块编码
  mn: string //模块名称
  pa: string //路径
  sr: string //屏幕分辨率
  nt: string //网络类型
  url: string //访问地址
  fl: string //系统语言
  ev: string //用户动作
  depthId: string //访问深度ID
}
interface ILogPage {
  requestUri: string
  serviceName: string
  page: number
  size: number
  createTimeStart: string
  createTimeEnd: string
  sortType: string
  columnName: string
}
interface IApiLogPage extends ILogPage {
  logType: string
}
interface ISafetyLogPage extends ILogPage {
  logType: string
  level: string
}
interface IlogLink {
  requestUri: string
  serviceName: string
  page: number
  size: number
  txId: string
  logType: string
  sortType: string
}

interface INormalLogPage {
  page: number
  size: number
  sortType: string
  sortName: string
}
type IBackupPage = INormalLogPage

interface IBackupLog {
  date: string
}
interface IDeleteLog {
  id: string
}
interface IBackupData {
  backupDate: string
  groupToken: string
  id: string
  size: string
  userFullname: string
}
interface ILogConfig {
  backupStorageDuration: number
  createBy: string
  createByName: string
  createDate: string
  id: string
  logStorageDuration: number
}
interface ILogConfigParams {
  logStorageDuration: number
  backupStorageDuration: number
}
interface ILogStatParams {
  param: string
}
interface ILogStat {
  dimension: string
  value: string
}
interface ICpu {
  cpuFreeRate: number
  cpuNum: number
  cpuSysRate: number
  cpuUsedRate: number
  cpuUserRate: number
  cpuWaitRate: number
  id: string
}
interface IMem {
  id: string
  memFree: string
  memFreeRate: number
  memTotal: string
  memUsed: string
  memUsedRate: number
}
export interface IDisk {
  diskDirName: string
  diskFree: string
  diskSysTypeName: string
  diskTotal: string
  diskTypeName: string
  diskUsed: string
  diskUsedRate: number
  id: string
}

interface IDiskParams {
  name: string
  time: string
}
interface IAlertData {
  informMethod: string[]
  lastTime: number
  state: number
  usedRate: number
  userNames: string[]
}
interface IMonitorDiskAlertExts {
  diskName: string
  diskUsedRate: string
  informMethod: string[]
  state: number
  userNames: string[]
}
interface IDiskData {
  sysMonitorDiskAlertExts: Array<IMonitorDiskAlertExts>
}
interface ICpuLatestData {
  informMethod: string[]
  lastTime: string
  state: number
  usedRate: number
  userNames: string
  userList: Array<IUserData>
}
interface IDiskLatestData {
  informMethod: string[]
  state: number
  diskName: string
  diskUsedRate: number
  userNames: string
  userList: Array<IUserData>
}
interface IMemLatestData {
  informMethod: string[]
  lastTime: string
  state: number
  usedRate: number
  userNames: string
  userList: Array<IUserData>
}
interface IUserData {
  avatarToken: string
  birthDay: string
  clientId: string
  email: string
  ext: string
  id: string
  introduction: string
  nickname: string
  osType: string
  phone: string
  registids: string
  sex: string
  tenantId: number
  userFullname: string
  userName: string
  userNo: string
}

interface IBehaviorPage {
  page: number
  size: number
  columnName: string
  sortType: string
  fc: string
  fn: string
  mc: string
  mn: string
  userName: string
  startDate: string
  endDate: string
}
//埋点
export function addMonitor(data: IMonitor | IMonitorPage = {} as IMonitor) {
  return request<IResponse<null>>({
    url: '/sys-monitor/analysis',
    method: 'post',
    data: data
  })
}
//错误日志
export function queryErrorLog(params: ILogPage) {
  return request<IPageResponse<null>>({
    url: '/sys-monitor/exceptionLog/page',
    method: 'get',
    params: params
  })
}
//接口日志
export function queryInterfaceLog(params: IApiLogPage) {
  return request<IPageResponse<null>>({
    url: '/sys-monitor/apiLog/page',
    method: 'get',
    params: params
  })
}
//安全事件
export function querySecurityLog(params: ISafetyLogPage) {
  return request<IPageResponse<null>>({
    url: '/sys-monitor/securityLog/page',
    method: 'get',
    params: params
  })
}
//日志链路
export function queryLogLink(params: IlogLink) {
  return request<IPageResponse<null>>({
    url: '/sys-monitor/log/tx',
    method: 'get',
    params: params
  })
}
//通用日志分页查询
export function queryNormalLog(params: INormalLogPage) {
  return request<IPageResponse<null>>({
    url: '/sys-monitor/normalLog/page',
    method: 'get',
    params: params
  })
}

//备份分页查询
export function queryBackupLogData(params: IBackupPage) {
  return request<IPageResponse<null>>({
    url: '/sys-monitor/log/backup/page',
    method: 'get',
    params: params
  })
}
//备份指定日期日志
export function backupLog(params: IBackupLog) {
  return request<IResponse<IBackupData>>({
    url: '/sys-monitor/log/backup',
    method: 'get',
    params: params
  })
}

//删除备份日志文件
export function deletebackupData(params: IDeleteLog) {
  return request<IResponse<IBackupData>>({
    url: '/sys-monitor/log/backup',
    method: 'delete',
    params: params
  })
}
//保存设置
export function saveLogConfig(data: ILogConfigParams) {
  return request<IResponse<ILogConfig>>({
    url: '/sys-monitor/log/config',
    method: 'post',
    data: data
  })
}
//获取当前正在使用的审计日志配置信息
export function queryLogConfig() {
  return request<IResponse<ILogConfig>>({
    url: '/sys-monitor/log/config/using',
    method: 'get'
  })
}

//按时间维度统计访问量
export function queryLogStat(params: ILogStatParams) {
  return request<IResponse<ILogStat[]>>({
    url: '/sys-monitor/log/stat',
    method: 'get',
    params: params
  })
}
//获取接口访问量排行
export function queryRank() {
  return request<IResponse<ILogStat[]>>({
    url: '/sys-monitor/log/stat/rank',
    method: 'get'
  })
}
//获取访问一个接口的平均耗时
export function queryAvgTime() {
  return request<IResponse<ILogStat[]>>({
    url: '/sys-monitor/log/stat/avg',
    method: 'get'
  })
}
//获取最新的cpu使用率数据
export function queryCPUData() {
  return request<IResponse<ICpu>>({
    method: 'get',
    url: '/sys-monitor/cpu'
  })
}

//获取距离当前时间多长时间的cpu列表信息
export function queryCPUChartData(time: string) {
  return request<IResponse<[{ sysRate: number[]; totalRate: number[]; userRate: number[] }, { CreateDate: string[] }]>>(
    {
      method: 'get',
      url: '/sys-monitor/cpus',
      params: {
        time: time
      }
    }
  )
}
//距离当前时间多长时间的内存列表信息
export function queryMEMChartData(time: string) {
  return request<IResponse<IMem[]>>({
    method: 'get',
    url: '/sys-monitor/memorys',
    params: {
      time: time
    }
  })
}
//获取最新的内存使用率数据
export function queryMEMData() {
  return request<IResponse<IMem>>({
    method: 'get',
    url: '/sys-monitor/mem'
  })
}
//获取磁盘共多少分区
export function queryDiskPartData() {
  return request<IResponse<IDisk[]>>({
    method: 'get',
    url: '/sys-monitor/disks/part'
  })
}
//获取最新的磁盘使用率数据
export function queryDiskAllRate(diskName: string) {
  return request<IResponse<IDisk>>({
    method: 'get',
    url: '/sys-monitor/disk',
    params: {
      diskName: diskName
    }
  })
}
//获取距离当前时间多长时间的磁盘列表信息
export function queryDiskData(params: IDiskParams) {
  return request<IResponse<IDisk[]>>({
    method: 'get',
    url: '/sys-monitor/disks',
    params: params
  })
}
//提交cpu警报参数，并插入到cpu报警数据库中
export function createCpuAlert(cpuAlertData: IAlertData) {
  return request<IResponse<string>>({
    method: 'POST',
    url: '/sys-monitor/cpus/alert',
    data: cpuAlertData
  })
}
//提交内存警报参数，并插入到内存报警数据库中
export function createMemAlert(alertData: IAlertData) {
  return request<IResponse<string>>({
    method: 'POST',
    url: '/sys-monitor/mems/alert',
    data: alertData
  })
}
//提交disk警报参数，并插入到disk报警数据库中
export function createDiskAlert(DiskData: IDiskData) {
  return request<IResponse<string>>({
    method: 'POST',
    url: '/sys-monitor/disks/alert',
    data: DiskData
  })
}
//获取最新的cpu保存的报警信息
export function queryCPUAlert() {
  return request<IResponse<ICpuLatestData | string>>({
    method: 'get',
    url: '/sys-monitor/cpu/latest/alert'
  })
}
//获取最新磁盘报警信息
export function queryDiskAlert() {
  return request<IResponse<IDiskLatestData[] | string>>({
    method: 'get',
    url: '/sys-monitor/disk/latest/alert'
  })
}
//获取最新内存报警信息
export function queryMEMAlert() {
  return request<IResponse<IMemLatestData | string>>({
    method: 'get',
    url: '/sys-monitor/mem/latest/alert'
  })
}
//获取用户行为数据
export function queryBehaviorData(params: IBehaviorPage) {
  return request<IPageResponse<null>>({
    url: '/sys-monitor/analysis/page',
    method: 'get',
    params: params
  })
}
interface IAu {
  endDate: string
  startDate: string
}
interface IAuData {
  count: string
  date: string
}
//获取用户活跃数接口
export function queryAu(params: IAu) {
  return request<IResponse<IAuData>>({
    url: '/sys-monitor/au',
    method: 'get',
    params: params
  })
}
interface IDv {
  avg: string
  date: string
}
//获取用户深度接口
export function queryDv(params: IAu) {
  return request<IResponse<IDv>>({
    url: '/sys-monitor/dv',
    method: 'get',
    params: params
  })
}
interface IAvUserView {
  count: string
  date: string
}
//获取人均浏览次数接口
export function queryAvgUserViews(params: IAu) {
  return request<IResponse<IAvUserView>>({
    url: '/sys-monitor/avgUserViews',
    method: 'get',
    params: params
  })
}
interface IPageViewParams {
  pageSize: number
  page: number
  endDate: string
  startDate: string
}
interface IPageView {
  count: number
  mc: string
  mn: string
  pa: string
  people: number
}
//不同页面浏览量
export function queryPageViews(params: IPageViewParams) {
  return request<IPageResponse<IPageView>>({
    url: '/sys-monitor/pageViews',
    method: 'get',
    params: params
  })
}
