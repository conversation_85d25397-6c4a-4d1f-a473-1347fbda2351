/*
 * @Author: your name
 * @Date: 2022-12-20 09:42:19
 * @LastEditTime: 2023-03-13 14:43:02
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \custom-app-plus\src\modules\AppCenter\Dashboard\api.ts
 */
import { request } from '@/fawkes'
import { IResponse } from '@/types'
import type { IModuleListItem } from '@/api/sys-form/application'
import type { IChartData, IChartInfo, IChartConfig } from './create-chart'

// 新增报表时查询模块列表所需的数据
export interface ISearchData {
  groupId: string
  moduleName: string
}
//查询模块列表
export function queryModuleList(data: ISearchData) {
  return request<IResponse<IModuleListItem[]>>({
    url: '/sys-form/group/module',
    method: 'get',
    params: data
  })
}

// 查询图列表
export function queryChartList(formKey: string) {
  return request<IResponse<IChartInfo[]>>({
    url: '/sys-form/form/chart/module',
    method: 'get',
    params: {
      formKey
    }
  })
}

// 获统计图数据
export function getChartData(data: IChartConfig) {
  return request<IResponse<IChartData>>({
    url: '/sys-form/form/analyzeData/analyze',
    method: 'post',
    data
  })
}

// 当前仪表盘的基础信息
export interface IBasicForm {
  dashboardName: string
  adminName: string
  adminFullName: string
  description: string
  dashboardIcon: string
  sort: string | number
  id: string
  groupId: string
  groupName: string
  dashboardConfig: string
  chartKey: string
  sysFormVisibleRange: ISysFormVisibleRange
  activeDashboardId: string
  menuId: string
  customColor: string
}
export interface ISysFormVisibleRange {
  isAuth: string
  roleUsers: string
  roleGroupCode: string
  roleGroup: string
  roleUsersFullname: string
  roleGroupFullname: string
  rolePost: string
  rolePostFullname: string
}
// 保存仪表盘
export function saveDashboard(data: IBasicForm) {
  return request<IResponse<IChartData>>({
    url: '/sys-form/form/chart/dashboard',
    method: 'post',
    data
  })
}

export type IReleaseData = {
  id: string
  type: 'dashboard' | 'module'
}
export function onReleaseModule(params: IReleaseData) {
  return request({
    url: '/sys-form/form/onRelease',
    method: 'post',
    params: params
  })
}
export interface IReleaseModuleData {
  appId: string
  formKey: string
  id: string
  moduleType: string
  roleId: string
  sysMenu: ISysMenu
}
export interface ISysMenu {
  formKey: string
  code: string
  component: string
  enable: boolean
  icon: string
  meta: string
  path: string
  sort: number | string
  title: string
  id: string
  isGeneral: number
  isAuth?: boolean
}
// 获取仪表盘数据
export interface IGetDashBoard {
  dashboardId: string
  groupId: string
  isDraft: boolean
}

// 保存仪表盘请求的数据
export interface ISaveAndReleaseDashboard {
  dashboardName: string
  adminName: string
  adminFullName: string
  description: string
  dashboardIcon: string
  sort: string | number
  id: string
  groupId: string
  groupName: string
  dashboardConfig: string
  chartKey: string
  portalId: string
  sysFormVisibleRange: ISysFormVisibleRange
  activeDashboardId: string
  menuId: string
  customColor: string
  formMenuParam: IReleaseModuleData
}

// 查看仪表盘
export function queryDashBoard(data: IGetDashBoard) {
  return request<IResponse<ISaveAndReleaseDashboard>>({
    url: '/sys-form/form/chart/dashboard',
    method: 'get',
    params: data
  })
}

// 更新发布仪表盘
export function saveAndReleaseDashboard(data: ISaveAndReleaseDashboard) {
  return request<IResponse<null>>({
    url: '/sys-form/form/saveAndReleaseDashboard',
    method: 'post',
    data
  })
}
