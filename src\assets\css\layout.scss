//root
[data-name='wp'] {
  #fks-app {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 0;
    margin: 0;
    border: hidden;

    .copyright {
      position: absolute;
      z-index: 1000;
      bottom: 10px;
      left: 50%;
      white-space: nowrap;
      color: #999;
      transform: translate(-50%);
    }

    > .copyright {
      margin-left: calc(#{$sideBarWidth} / 2);
      transition: margin-left 0.15s;
    }

    //App > .main-container
    .main-container {
      position: relative;
      // min-height: 100%;
      // margin-left: $sideBarWidth;
      background: #f0f2f6;
      transition: margin-left 0.15s;
      padding: 0 0 0 24px;
      //App > .main-container > fks-app-main
      .fks-app-main {
        position: relative;
        box-sizing: border-box;
        width: 100%;
        height: calc(100vh - 56px);
        padding: 16px;
        overflow: auto;

        &.tags-view {
          padding: 0 24px 16px 24px;
        }

        &.footer-view {
          padding: 16px 16px 0;
        }

        &.tags-view.footer-view {
          padding: 0 16px 40px;
        }

        &.tags-view,
        &.footer-view {
          height: calc(100vh - 128px);
        }

        &.full-screen {
          height: 100vh !important;
          padding: 0 !important;
          background-color: rgb(240, 242, 246) !important;
        }
      }
    }

    .logo-container {
      position: relative;
      flex: none;
      box-sizing: border-box;
      width: 448px;
      height: 72px;
      overflow: hidden;
      line-height: 72px;
      text-align: center;
      // background: mix(#000, $theme, 10%);
      transition: width 0.15s;
      // padding: 8px 0 0 32px;
      padding-left: 32px;
      box-sizing: border-box;

      .logo {
        transition: transform 0.14s;
      }
      & .logo-link {
        width: 100%;
        height: 100%;
        display: block;
        display: flex;
        align-items: center;
        & .logo {
          width: 100%;
          height: auto;
        }
      }
    }

    //Navbar
    .head-navbar-container {
      z-index: 1002;
      height: 72px;
      position: relative;
      background: linear-gradient(270deg, #052d62 0%, #002249 100%);
      border-radius: 15px;
      margin: 16px 24px 0 24px;
    }
    //searchbar
    .fks-searchbar .search-input > .fks-row {
      display: block;
    }
    //Sidebar
    .sidebar-container {
      // position: fixed;
      z-index: 1001;
      top: 112px;
      bottom: 0;
      width: $sideBarWidth !important;
      height: 100%;
      overflow: hidden;
      font-size: 0;
      // background: linear-gradient(180deg, rgba(0, 0, 0, .1) 0%, rgba(0, 0, 0, .15) 79%, rgba(0, 0, 0, .2) 100%), var(--theme-color);
      background: #ffffff;
      border-radius: 15px;
      transition: width 0.15s;
      padding: 24px 16px;
      box-sizing: border-box;
      // reset fawkes-lib css
      .horizontal-collapse-transition {
        transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
      }

      .scrollbar-wrapper {
        overflow-x: hidden !important;
      }

      .fks-scrollbar__bar.is-vertical {
        right: -8px;
      }

      .fks-scrollbar {
        height: calc(100% - 160px);
      }

      .is-horizontal {
        display: none;
      }

      a {
        display: inline-block;
        width: 100%;
        overflow: hidden;
        border-radius: 2px;
      }

      .fks-menu {
        width: 100% !important;
        height: 100%;
        border: none;
        background: transparent !important;
        .fks-submenu.is-opened {
          background: rgba(0, 0, 0, 0.04);
          border-radius: 8px;
        }
        &-item.is-active {
          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 40px;
            background-color: rgba($theme, 0.12);
          }

          &::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba($theme, 0.12) !important;
            opacity: 0.1;
          }
        }
        &-item:focus {
          color: #525a65;
          background-color: $theme;
        }
      }

      // menu hover
      .fks-submenu__title {
        i {
          color: #525a65 !important;
        }

        &:hover {
          color: var(--theme-color);
          background-color: rgba($theme, 0.12);
        }
      }

      .is-active {
        &:not(.is-opened) {
          color: #fff !important;
          background-color: $theme;
          border-radius: 8px;
          transition: background-color 0.15s 0.15s;
          .fks-submenu__title {
            color: #fff !important;
            i {
              color: #fff !important;
            }
          }
        }
        // >.fks-submenu__title {
        //   color: #fff;
        //   background-color: $theme !important;
        // }

        // &.is-opened .is-active.is-opened .fks-submenu__title {
        //   background-color: rgba($theme, .8) !important;
        // }

        // &.is-opened > .fks-submenu__title {
        //   background-color: rgba($theme, .6) !important;
        // }
      }
    }

    //App > hideSidebar > main-containner
    &.hideSidebar {
      .main-container {
        padding-left: 0;
        margin-left: 0 !important;
      }

      > .copyright {
        margin-left: 0 !important;
      }
    }

    //App > collapseSidebar
    &.collapseSidebar {
      .sidebar-container {
        width: #{$collapseBarWith} !important;

        .fks-submenu {
          overflow: hidden;

          & > .fks-submenu__title {
            padding: 0 !important;

            .svg-icon {
              margin-left: 20px;
            }

            .fks-submenu__icon-arrow {
              display: none;
            }
          }
        }

        .fks-menu--collapse {
          .fks-submenu {
            & > .fks-submenu__title {
              & > span {
                display: inline-block;
                width: 0;
                height: 0;
                overflow: hidden;
                visibility: hidden;
              }
            }
          }
        }
      }

      .main-container {
        margin-left: $collapseBarWith;
      }

      > .copyright {
        margin-left: calc(#{$collapseBarWith} / 2);
      }

      .logo-container {
        width: $collapseBarWith;

        .logo {
          transform: translateX(8px);
        }
      }
    }

    .fks-menu--collapse .fks-menu .fks-submenu {
      min-width: $sideBarWidth !important;
    }

    // mobile responsive
    &.mobile {
      position: fixed;
      top: 0;

      .main-container {
        margin-left: 0;
      }

      .sidebar-container {
        width: $sideBarWidth !important;
      }

      &.collapseSidebar {
        .sidebar-container {
          transform: translate3d(-$sideBarWidth, 0, 0);
          transition-duration: 0.3s;
          // .is-active::after: none;
        }
      }
    }

    .withoutAnimation {
      .main-container,
      .sidebar-container,
      > .copyright {
        transition: none;
      }
    }

    .fks-menu .svg-icon {
      margin-right: 5px;
    }

    @include clearfix;
  }

  .sidebar-container,
  .fks-menu--vertical .nest-menu {
    & .fks-submenu > .fks-submenu__title,
    & .fks-submenu .fks-menu-item,
    & .fks-menu-item {
      height: $menuItemHeight;
      line-height: $menuItemHeight;
      color: #525a65;
      //min-width: $sideBarWidth !important;
      // background-color: $subMenuBg !important;
      border-radius: 8px;

      i {
        color: #525a65;
      }

      &:hover {
        color: var(--theme-color);
        background-color: rgba($theme, 0.12);
      }

      &.is-active {
        background-color: rgba($theme, 0.12);
      }
    }
  }

  .fks-menu--vertical {
    :not(.nest-menu) .fks-submenu > .fks-submenu__title span + span {
      margin-left: 24px;
    }

    .nest-menu .fks-submenu > .fks-submenu__title,
    .fks-menu-item {
      &:hover {
        // you can use $subMenuHover
        background-color: $menuHover !important;
      }
    }

    // the scroll bar appears when the subMenu is too long
    > .fks-menu--popup {
      max-height: 100vh;
      overflow-y: auto;
      background: mix(#000, $theme, 10%);
      background-color: none !important;

      &::-webkit-scrollbar-track-piece {
        background: #d3dce6;
      }

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        border-radius: 20px;
        background: #99a9bf;
      }
    }
  }
  .fks-message-box.developerMode {
    width: 400px !important;
    height: 185px !important;
    padding: 0 !important;

    .fks-message-box__content {
      height: 90px !important;
      padding: 16px 16px 0 !important;
    }
  }
  .fks-date-editor .fks-range-separator {
    width: 10% !important;
  }
}
.fks-query-page .fks-query-list-name {
  .fks-title-icon {
    width: 19px;
    height: 19px;
    background: linear-gradient(135deg, #bad8ff 0%, #d9e9fb 37%, #e3f1fe 70%, #eff5fc 100%);
    box-shadow: inset 1px 1px 1px 0px #f1f7ff;
    border-radius: 1px;
    margin-right: 10px;
    &::before {
      width: 11px;
      height: 11px;
      background: linear-gradient(135deg, #0677f9 0%, #5c9cfe 100%);
      box-shadow: inset 1px 1px 1px 0px #7db0fc;
      border-radius: 1px;
      left: 13px;
      top: 13px;
    }
  }
}
