/*
 * @Author: <EMAIL>
 * @Date: 2019-11-07 09:36:15
 * @LastEditors: chen_yt
 * @LastEditTime: 2022-08-19 14:39:38
 * @Description: 应用相关接口
 */
import { request } from '@/fawkes'
import { resetMessage } from '@/utils/message'
import { GRANT_TYPE } from '@/fawkes/config/constant'
import Qs from 'qs'
import { IResponse } from '@/types'
import { IOauthUser } from '@/types/user'
export interface ILoginForm {
  grant_type: `${GRANT_TYPE}`
  username: string
  captcha_code: string
  captcha_key: string
}
export interface ILoginForm {
  grant_type: `${GRANT_TYPE}`
  access_token: string
  password: string
  scope?: string
  username: string
}

/**
 * @description: 获取token
 * @param {*}
 * @return {*}
 */
export function queryToken(data: ILoginForm): Promise<IOauthUser> {
  return new Promise(async (resolve, reject) => {
    request<IOauthUser>({
      url: '/sys-auth/oauth/token',
      method: 'post',
      data: {
        scope: 'all',
        ...data
      },
      transformRequest: [
        function (data) {
          data = Qs.stringify(data) //序列化参数
          return data
        }
      ]
    })
      .then((res) => {
        resolve(res)
      })
      .catch((e) => {
        resetMessage.warning(e.response.data)
        reject(e)
      })
  })
}
export interface IOauthLoginParams {
  grant_type?: string
  username?: string
  password?: string
  captcha_type?: string
  captcha_key?: string
  captcha_code?: string
}

//统一认证登录
export function oauthLogin(data: IOauthLoginParams) {
  return request<IOauthUser>({
    url: '/sys-sso/oauth2/login',
    method: 'post',
    data: {
      scope: 'all',
      ...data
    },
    transformRequest: [
      function (data) {
        data = Qs.stringify(data) //序列化参数
        return data
      }
    ]
  })
}
/**
 * @description: 登出
 * @param {*}
 * @return {*}
 */
/** */
export async function loginOut(userName?: string) {
  return (
    await request<IResponse<null>>({
      url: '/sys-auth/oauth/exit',
      params: {
        userName
      },
      method: 'delete'
    })
  ).status
}

/**
 * @description: 第三方应用登录
 * @param {*} token
 * @return {*}
 */
export function querySocialInfoByToken(token: string) {
  return request<IOauthUser>({
    method: 'get',
    url: '/sys-auth/oauth/user_info',
    headers: {
      Authorization: 'bearer ' + token
    }
  })
}

/**
 * @description: 获取服务器时间戳
 * @param {*}
 * @return {*}
 */
export function queryTs() {
  return request<number>({
    method: 'GET',
    url: '/sys-gateway/sign/ts',
    headers: {
      'Cache-Control': 'no-cache'
    },
    timeout: 1000
  })
}

export function logoutUsers(data: { clientId?: string } = {}) {
  return request<IResponse<null>>({
    url: '/sys-auth/oauth/client/exit',
    params: data,
    method: 'delete'
  })
}
/**
 * 获取短信验证码
 * @param data
 * @returns
 */
export function getSMSCaptcha(phone: string) {
  return request<IResponse<string>>({
    url: '/sys-auth/oauth/sms_captcha',
    params: {
      phone
    },
    method: 'get'
  })
}
/**
 * 获取当前登录人登录错误次数
 * @param data
 * @returns
 */
export function getErrorParams(data?: { userName: string }) {
  return request<IResponse>({
    url: '/sys-auth/oauth/user/lock/num',
    params: data,
    method: 'get'
  })
}
/**
 * 获取图形验证码
 * @returns
 */
export function getImgCaptcha() {
  return request<{ key: string; img: string }>({
    url: '/sys-auth/oauth/img_captcha',
    params: {
      height: 43,
      width: 110,
      len: 4
    },
    method: 'get'
  })
}
interface ICaptchaVO {
  browserInfo?: string
  captchaFontSize?: number //字体大小
  captchaFontType?: string //字体类别
  captchaId?: string
  captchaOriginalPath?: string
  captchaVerification?: string
  clientUid?: string
  jigsawImageBase64?: string
  originalImageBase64?: string
  point?: {
    secretKey?: string
    x?: number
    y?: number
  }
  pointJson?: string
  pointList?: { x?: number; y?: number }[]
  projectCode?: string
  result?: boolean
  secretKey?: string
  token?: string
  ts?: number
  wordList?: string[]
  captchaType?: string //验证模式
}
interface ICaptchaData {
  repCode: string
  repMsg: string | null
  repData: {
    captchaId: string | null
    projectCode: string | null
    captchaType: string | null
    captchaOriginalPath: string | null
    captchaFontType: string | null
    captchaFontSize: number | null
    secretKey: string
    originalImageBase64: string
    point?: {
      secretKey?: string
      x?: number
      y?: number
    }
    jigsawImageBase64: string
    wordList: string[]
    pointList: { x?: number; y?: number }[]
    pointJson: string
    token: string
    result: boolean
    captchaVerification: string | null
    clientUid: string | null
    ts: string | null
    browserInfo: string | null
  }
  success: true
}
export function reqGet(data: ICaptchaVO) {
  return request<ICaptchaData>({
    url: '/sys-auth/oauth/behaviour_captcha',
    method: 'post',
    data
  })
}

export function reqCheck(data: ICaptchaVO) {
  return request<ICaptchaData>({
    url: '/sys-auth/oauth/check_behaviour_captcha',
    method: 'post',
    data
  })
}
