/*
 * @Author: your name
 * @Date: 2023-02-21 09:06:13
 * @LastEditTime: 2023-03-09 15:47:13
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \custom-app-plus\src\api\sys-form\custom-app.ts
 */

import { request } from '@/fawkes'
import type { IResponse } from '@/types/'

// 组件的类型
export type IComponentName =
  | 'form-divider'
  | 'notify'
  | 'associate-data'
  | 'user-selector'
  | 'org-selector'
  | 'post-selector'
  | 'input'
  | 'comment'
  | 'upload'
  | 'select'
  | 'date'
  | 'detail-table'
  | 'form_switch'
  | 'rate'
  | 'check-box'
  | 'information'
  | 'radio'
  | 'count'
  | 'amount'
  | 'number'
  | 'time'

export interface IOriFormField {
  tagAttribute: IComponentName
  name: string
  field: string
}

export interface IForm {
  value: string
  label: string
  version: string
}

export type IFormTreeNode = IForm & {
  children: IForm[]
}

//查询应用列表
export function queryAppList(params: { formType: 3 | 2 | 1 | 0 }) {
  return request<IResponse<IFormTreeNode[]>>({
    url: '/sys-form/group/tree',
    method: 'get',
    params
  })
}

// 获取表单项列表
export async function queryFormFields(formKey: string): Promise<IOriFormField[]> {
  const res = await request<IResponse<IOriFormField[]>>({
    url: '/sys-form/module/field',
    method: 'get',
    params: {
      formKey
    }
  })
  return res.data.filter((field) => !['form-divider', 'notify', 'associate-data'].includes(field.tagAttribute))
}

//保存/更新部署自定义流程
export function queryPublicLink(params: { formKey: string; publicFormUrl: string }) {
  return request<IResponse<string>>({
    url: '/sys-form/form/publicLink',
    method: 'GET',
    params
  })
}
