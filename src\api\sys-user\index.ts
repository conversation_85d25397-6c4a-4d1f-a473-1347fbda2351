/*
 * @Author: <EMAIL>
 * @Date: 2019-11-07 09:36:15
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2021-09-06 11:27:00
 * @Description: 用户相关接口
 */
import { request } from '@/fawkes'
import { useAppConfig, useApp } from '@/fawkes/store'
import { IButton, IFksRouteData, IPageResponse, IResponse } from '@/types'

import { IOauthUser, IUserInfo, IUserBase } from '@/types/user'
import type { AxiosRequestConfig, AxiosResponse } from 'axios'

import { ILoginForm } from '../sys-auth'

/**查询用户菜单 */
export async function queryRouter() {
  return request<IResponse<IFksRouteData[]>>({
    url: '/sys-user/user/menus',
    method: 'get',
    params: {
      portalId: useApp().portal.id
    }
  })
}

/**查询用户菜单下按钮 */
export function queryPermissions(menuId?: string) {
  return request<IResponse<IButton[]>>({
    url: '/ym/button/user/buttons',
    method: 'get',
    params: {
      menuId,
      portalId: useApp().portal.id
    }
  })
}
export interface IPortal {
  id: string //门户id
  name: string //门户名
  parentId: string //父门户id
  parentName: string //父门户名
  pathID: string //树路径id
  pathName: string //树路径名
  relationId: string //关联id
  tenantId: string //租户id
  type: number //门户类型
}

/**获取所有门户 */
export function queryPortals(data?: { userId?: string; portalId?: string }) {
  return request<IResponse<IPortal[]>>({
    url: '/sys-user/user/portals',
    method: 'GET',
    params: data
  })
}
export function queryPortalsInOtherTenant(tenantConfig: {
  tenantId: string | number
  clientId: string
  clientSecret: string
}) {
  return request<IResponse<IPortal[]>>({
    url: '/sys-user/user/portals',
    method: 'GET',
    tenantConfig
  } as AxiosRequestConfig)
}

export async function queryUserInfo(data: { userName: string; portalId: string }) {
  const res = await request<IResponse<IUserInfo>>({
    url: '/sys-user/userInfo',
    method: 'get',
    params: data
  })
  return res.data
}
export async function queryCurrentUserInfo(data: { userName: string; portalId: string }) {
  const res = await request<IResponse<IUserInfo>>({
    url: '/sys-user/currentUserInfo',
    method: 'get',
    params: data
  })
  return res.data
}
export function queryUserCache() {
  return request<IResponse<null>>({
    url: '/sys-user/users/cache/sync',
    method: 'get'
  })
}
export function queryAuthCache() {
  return request<IResponse<null>>({
    url: '/sys-user/user/auth/cache/sync',
    method: 'get'
  })
}
/**
 * @description: 绑定第三方应用
 * @param {*} data
 * @return {*}
 */
export function socialBind(data: ILoginForm) {
  return request<IOauthUser>({
    method: 'POST',
    url: '/sys-user/oauth/user/bind',
    data: {
      access_token: data.access_token,
      grant_type: data.grant_type,
      password: data.password,
      scope: data.scope ? data.scope : 'all',
      username: data.username
    },
    ctM: true
  } as AxiosRequestConfig)
}
/**
 * 发送用户数据变更短信
 * @param data
 * @returns
 */
export function getSms(phone) {
  return request<IResponse<string>>({
    url: '/sys-user/user/sms_captcha',
    params: { phone },
    method: 'get'
  })
}

/**
 * 获取用户注册短信
 * @param data
 * @returns
 */
export function getRegisterSMSCaptcha(phone) {
  return request<IResponse>({
    url: '/sys-user/user/sms_register_captcha',
    params: {
      phone
    },
    method: 'get'
  })
}
interface IRetrieveUserPwdParam {
  captcha: string //验证码
  key: string //key
  phone: string //手机号
  pwd: string //密码sm4加密
}
/**
 * 通过短信验证码找回密码
 * @param data
 * @returns
 */
export function editPassword(data: IRetrieveUserPwdParam) {
  return request<IResponse>({
    url: '/sys-user/user/retrieve/pwd',
    data: data,
    method: 'put'
  })
}

export interface IRegisterParam {
  accountPeriod?: number // 账号周期
  accountStatus?: number // 账号状态
  age?: number // 年龄
  avatarToken?: number // 头像token
  birthDay?: string // 生日
  captcha?: string // 验证码
  citizenship?: string // 国籍
  degree?: string // 学位
  education?: string // 学历
  email?: string // 邮箱
  emergencyName?: string // 紧急联系人姓名
  emergencyPhone?: string // 紧急联系人电话
  formerName?: string // 曾用名
  groupToken?: string // 文件组
  hiredate?: string // 入职时间
  id?: string // ID
  idcardNumber?: string // 证件号码
  idcardType?: string // 证件类型
  introduction?: string // 个人简介
  isInitPwd?: boolean // 是否更新初始密码
  jobNumber?: string // 工号
  key?: string // 验证码key
  lastActiveTime?: string // 最后活跃时间
  lastUpdatePwdTime?: string // 最后更新密码时间
  majorName?: string // 专业名称
  marriageState?: number // 婚姻状态
  nation?: string // 民族
  nationality?: string // 国籍
  nativePlace?: string // 籍贯
  newPost?: string // newPost
  nickname?: string // 昵称
  officeLocation?: string // 办公地点
  officePhone?: string // 办公电话
  orgNoList?: string[] // 岗位Id集合
  otherPhone?: string // 其他电话
  password?: string // 密码
  phone?: string // 手机号
  photoToken?: string // 照片token
  politics?: string // 政治面貌
  postIdList?: string[] // 岗位Id集合
  remark?: string // 备注
  sex?: string // 性别
  signToken?: string // 电子签名附件token
  sort?: number // 顺序
  stature?: string // 身高
  tenantId?: number // 租户ID
  title?: string // 职务
  userFullname?: string // 姓名
  userName?: string // 用户名
  userNo?: string // 员工号
  userType?: number // 用户类型
  weight?: string // 体重
  workingSeniority?: string // 工作年限
}
export function userRegister(data: IRegisterParam) {
  return request<IResponse<IRegisterParam>>({
    url: '/sys-user/user/register',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export interface IQueryUsersPageParam {
  accountStatus?: number //账号状态
  columnName?: string //排序字段名
  needOrg?: boolean //是否需要用户组织机构
  noOrg?: boolean //筛选没有组织的人
  orgNo?: string //组织机构id
  pageNo?: number //当前页
  pageSize?: number //每页条数
  portalId?: string //门户ID
  searchValue?: string //搜索条件
  sort?: string //排序规则
}

export function queryUsersPage(params: IQueryUsersPageParam) {
  return request<IPageResponse<IUserInfo>>({
    url: '/sys-user/users/page',
    method: 'get',
    params: {
      ...params
    }
  })
}
export interface IPwdParam {
  newPwd: string // 新密码
  oldPwd: string // 旧密码
}

export interface IPhoneParam {
  captcha?: string //验证码
  key?: string
  phone?: string
  userName?: string
  id?: string
}

//修改密码
export function updatePassword(data: IPwdParam) {
  return request<IResponse>({
    url: '/sys-user/user/pwd',
    method: 'put',
    data: data
  })
}
interface IAccountUserInfo extends IUserBase {
  uuid: string
  company: string
  source: string
}
//获取当前用户第三方绑定账号信息
export function queryExtraData(data: { source?: string; userId?: string }) {
  const { tenantId, clientId } = useAppConfig().clientConfig
  const defaultMainTenantConfig = { tenantId, clientId }
  return request<IResponse<IAccountUserInfo[]>>({
    url: '/sys-user/oauth/users',
    method: 'get',
    params: data,
    tenantConfig: defaultMainTenantConfig
  })
}
//更新用户手机号
export function updatePhone(data: IPhoneParam) {
  return request<IResponse>({
    url: '/sys-user/user/change/phone',
    method: 'put',
    data: data
  })
}
//更新手机号获取验证码
export function queryPhoneCode(data: { phone?: string }) {
  return request<IResponse>({
    url: '/sys-user/user/sms_change_phone_captcha',
    method: 'get',
    params: data
  })
}
//更新用户邮箱
export function updateEmail(data: { userName: string; email: string; id: string }) {
  return request<IResponse>({
    url: '/sys-user/user/information',
    method: 'put',
    data: data
  })
}
//第三方应用解绑
export function updateExtra(data: { id: string }) {
  const { tenantId, clientId } = useAppConfig().clientConfig
  const defaultMainTenantConfig = { tenantId, clientId }
  return request({
    url: '/sys-user/oauth/user',
    method: 'DELETE',
    params: data,
    tenantConfig: defaultMainTenantConfig
  } as AxiosRequestConfig)
}
// 第三方登录信息
export function authenticationLogin(data: string) {
  return request<IResponse>({
    url: data,
    method: 'get'
  })
}

interface ISessionParams {
  columnName?: string
  pageNo?: number
  pageSize?: number
  searchValue?: string
  sort?: string
}
// 获取用户会话分页
export function querySessionList(params: ISessionParams) {
  return request<IResponse>({
    url: '/sys-user/users/session/page',
    method: 'get',
    params
  })
}
export interface IExportUsersParams {
  exportAll?: boolean
  idList?: string[]
  searchValue?: string
}
//导出人员
export function exportUsers(data: IExportUsersParams) {
  return request<AxiosResponse<Blob>>({
    url: '/sys-user/users/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
//导出账号信息
export function exportAccounts(data: IExportUsersParams) {
  return request<AxiosResponse<Blob>>({
    url: '/sys-user/accounts/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
//导出用户模板
export function downloadUserTemplates() {
  return request<AxiosResponse<Blob>>({
    url: '/sys-user/account/template',
    method: 'get',
    responseType: 'blob'
  })
}
//导入人员模板
export function downloadPeopleTemplates() {
  return request<AxiosResponse<Blob>>({
    url: '/sys-user/user/template',
    method: 'get',
    responseType: 'blob'
  })
}
//下载岗位导入模板
export function downloadPostTemplates() {
  return request<AxiosResponse<Blob>>({
    url: '/sys-user/post/template',
    method: 'get',
    responseType: 'blob'
  })
}
//下载部门导入模板
export function downloadOrgTemplates() {
  return request<AxiosResponse<Blob>>({
    url: '/sys-user/org/template',
    method: 'get',
    responseType: 'blob'
  })
}
export interface IImportParams {
  file: File
  portalId: string
}
//导入用户
export function importUser(data: FormData) {
  return request<IResponse>({
    url: '/sys-user/accounts/import',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
//导入人员
export function importPeople(data: FormData) {
  return request<IResponse>({
    url: '/sys-user/users/import',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
//导入岗位
export function importPost(data: FormData) {
  return request<IResponse>({
    url: '/sys-user/posts/import',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
//导入部门
export function importOrg(data: FormData) {
  return request<IResponse>({
    url: '/sys-user/orgs/import',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
