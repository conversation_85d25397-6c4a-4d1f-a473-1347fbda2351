import { request } from '@/fawkes'
import { IResponse } from '@/types'

export function queryDataSourceList(params?: object) {
  return request<IResponse<any>>({
    url: '/platform-etl/v1/etl/db-conn',
    method: 'get',
    params
  })
}

export function connectTest(data) {
  return request<IResponse<any>>({
    url: '/platform-etl/v1/etl/db-conn/test',
    method: 'post',
    data
  })
}

export function createConnection(data) {
  return request<IResponse<any>>({
    url: '/platform-etl/v1/etl/db-conn',
    method: 'post',
    data
  })
}

export function viewConnection(id) {
  return request<IResponse<any>>({
    url: `/platform-etl/v1/etl/db-conn/${id}`,
    method: 'get'
  })
}

export function editConnection(id, data) {
  return request<IResponse<any>>({
    url: `/platform-etl/v1/etl/db-conn/${id}`,
    method: 'put',
    data
  })
}

export function delConnection(id) {
  return request<IResponse<any>>({
    url: `/platform-etl/v1/etl/db-conn/${id}`,
    method: 'delete'
  })
}
