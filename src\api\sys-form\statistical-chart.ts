/*
 * @Author: your name
 * @Date: 2023-03-10 14:31:03
 * @LastEditTime: 2023-03-13 09:47:09
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \custom-app-plus\src\api\sys-form\statistical-chart.ts
 */
import { request } from '@/fawkes'
import type { IResponse } from '@/types/'
import type { IChartInfo } from '@/api/sys-form/create-chart'

// 根据formKey或去报表列表
export function queryChartList(formKey: string) {
  return request<IResponse<IChartInfo[]>>({
    url: 'sys-form/form/chart/list',
    method: 'get',
    params: {
      formKey
    }
  })
}

// 根据报表ID删除已创建的报表
export function deleteChart(chartId: string) {
  return request<IResponse<null>>({
    url: '/sys-form/form/chart/id',
    method: 'delete',
    params: {
      chartId
    }
  })
}
