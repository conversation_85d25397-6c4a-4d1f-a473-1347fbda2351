import { request } from '@/fawkes'
import type { IResponse } from '@/types/'
import { IComponentName } from '../sys-form/custom-app'

// 组件的类型
// 表单的组件
export type IPairedField<T extends IComponentName = IComponentName> = {
  componentName: T
  fieldName: string // 字段的名称，如 field101
  fieldLabel: string
  nodeId: string
  isMultiple?: 'false' | 'true' | null
  customValue?: string
  sourceType?: string //参数来源，自定义参数用"custom"
  formKey?: string
  formName?: string
  formVersion?: string
  columnName?: string
  columnLabel?: string
  subWpId?: string //用于多实例参数中
}
// 暴露在全局的表单
export interface IPairedModule {
  nodeId: string // 图形节点的id unitId.slice(0, 5) === "subWp" "custom"
  moduleName: string
  fields: IPairedField[]
  // formKey?: string
  // version?: string
  // connectorId?: string
}
export interface IBindedForm {
  formKey: string
  formName: string
  version: string
}
// 绑定表单的状态
export enum EFormStatus {
  started = 'started',
  finished = 'finished'
}
export interface IMatchingParam {
  sourceField: null | IPairedField // 匹配的源
  targetFieldName: string // 匹配的目标
  inflowForm?: IPairedModule[] // 可以匹配的表单
  hidden?: boolean // 仅作为存储数据时为true
}

interface INodeBaseProperties {
  parentId?: string
  nodeProcessState?: string //节点工作流状态
  beInfluenced?: boolean //节点设置是否被影响
  validated?: boolean //节点信息是否完整
}

export interface IBaseFormProperties extends INodeBaseProperties {
  bindedForm: IBindedForm // 绑定的表单
  outputParams: IPairedField[] //输出参数配置
  outputFields: IPairedField[] //输出资源配置
}

// 工作包表单、流程表单节点属性-关联表单
export interface IAttachedForm {
  formKey: string
  formName: string
  version: string
  // formType: IFormType
}

export interface IFormProperties extends IBaseFormProperties {
  principal: IPairedField // 负责人
  completionConditions: EFormStatus
  paramMappings: IMatchingParam[] // 参数映射
  attachedForms: IAttachedForm[] // 关联表单
  attachedFields: IPairedField[] // 关联字段
}

export interface IMessageProperties extends INodeBaseProperties {
  msgParam: string
  previewContent: object
  principal: IPairedField // 收件人
  template: string //模板id
}

export interface IConnectorCondition {
  id: string
  ruleName: string
  actionList: []
  list: {
    id: string
    list: IConnectorConditionVO[]
  }[]
}
export interface IConditionRule {
  isCustomTag: boolean
  tagAttribute: IComponentName
  rule?: string
  id?: string
  customLabel?: string
  fileList?: []
  config?: object
}

export interface IConnectorConditionVO extends IConditionRule {
  value: string | { selectorId: string; selectorName: string }
  value2?: string
  field?: string
  inflowForm?: IPairedModule[]
  otherRule?: IConnectorConditionVO
  isDelete?: boolean
  label?: string
  ReplacementValue?: string
  ReplacementValue2?: string
  showCustomSelect: boolean
  tag: string
  customValue?: {
    value: string
  }
}
export interface ISubPackageProperties extends INodeBaseProperties {
  multiInstParam?: IPairedField | null
  completeConditionType: string
  completeCondition: string
}

export interface IColumnConfig {
  __vModel__?: string
  label?: string
  value?: string
  showCustomSelect: boolean
  fileList: []
  isCustomTag: boolean
  customValue: string
  customLabel: string
  inflowForm: IPairedModule[]
  tagAttribute: IComponentName
  tag: string
  conditionId?: string
  config: {
    placeholder: string
    options: { label: string; value: string }[]
    multipleLimit: number
    valueFormat: string
    isRange: boolean
    multiple: boolean
    dateType: string
    min: number | string
    max: number | string
    step: string
  }
}

export interface IColumnParam {
  sourceField: IPairedField
  targetFieldName: string
  columnConfig: IColumnConfig
  // inflowForm: IPairedModule[]
}

export interface IDataHandlerProperties extends INodeBaseProperties {
  bindedForm?: IBindedForm // 绑定的表单
  principal: IPairedField
  fieldsForm?: object
  columnParamList: IMatchingParam[]
  condition?: string
  conditionList?: IConnectorCondition[]
}

export interface IConnectorParamConfig {
  nodeId: string
  showName: string
  isRequired: '0' | '1'
  targetFieldName: string //connectorParamName
  sourceField: IPairedField
  componentName: IComponentName
}

export interface IConnectorProperties extends INodeBaseProperties {
  connectorId: string
  connectorName: string
  query: IConnectorParamConfig[]
  body: IConnectorParamConfig[]
  header: IConnectorParamConfig[]
  path: IConnectorParamConfig[]
  outputParams?: IPairedField[] //输出参数配置
  outputFields?: IPairedField[] //输出资源配置
}
export type ITriggerType = 'byForm' | 'byTime'
// export type IStartProperties<T extends ITriggerType = any> = T extends 'byForm'
//   ? IByForm
//   : T extends 'byTime'
//   ? IByTime
//   : IByForm & IByTime
export type IStartProperties = IByFormStartProperties & IByTimeStartProperties
export interface IByFormStartProperties extends IBaseFormProperties {
  triggerMode: ITriggerType
}
// 频率的单位
export type IFrequencyUnit = 'hour' | 'week' | 'month' | 'year'
export interface IByTimeStartProperties extends INodeBaseProperties {
  triggerMode: ITriggerType
  startTime: string // 开始时间
  endTime: string // 结束时间
  frequencySpace: number // 频率
  frequency: IFrequencyUnit // 频率的单位
  frequencyWeeks: string // 周几 ,分割
  frequencyDate: string
}
// export interface IStartProperties extends INodeBaseProperties {
//   startTime: string
//   endTime: string
//   frequencySpace: number
//   frequency: string
//   frequencyDate: string
//   frequencyWeeks: string[]
// }

// 图形节点的自定义属性
export type INodeProperties =
  | IFormProperties
  | IDataHandlerProperties
  | IConnectorProperties
  | ISubPackageProperties
  | IMessageProperties
  | IStartProperties
  | IByFormStartProperties

export interface IGraphData<T extends INodeProperties = INodeProperties> {
  edges: IEdge[]
  nodes: INode<T>[]
}
export interface IEdge {
  id: string
  targetNodeId: string
  sourceNodeId: string
  type: string
}

// 图形节点的类型
export type INodeType =
  | 'start'
  | 'end'
  | 'form' // 表单
  | 'subWp'
  | 'approve' // 流程表单
  | 'dataInsert'
  | 'dataDelete'
  | 'dataModify'
  | 'sms'
  | 'email'
  | 'socket'
  | 'polyline'
  | 'connector'
  | 'commonProperty'

// 图形节点
export interface INode<T extends INodeProperties = INodeProperties> {
  id: string
  x: number
  y: number
  title?: string // 父工作包时用到
  text?: {
    x: number
    y: number
    value: string
  }
  type: INodeType
  children?: string[]
  properties?: T
}
export interface IWorkPackageResponse {
  id?: string
  wpStatus?: string
  sceneColor: string
  wpIcon: string
  wpName: string
  wpDesc: string
  wpKey: string
  portalId: string
  wpRawData: string // 图形数据
  wpDesign: string // 全局资源&全局参数
}
export interface IParam {
  key: string
  label: string
  multiple: string
  type: string
  value: string | ISelectorValue
}
export interface ISelectorValue {
  username?: string
  userfullname?: string
  deptId?: string
  deptName?: string
  postId?: string
  postName?: string
}

// export interface ISubField {
//   key: string
//   label: string
//   type: string
//   value: string
// }

export interface IWPDesign {
  customParams: IPairedField[]
  multipleInstancesParams: IPairedField[]
}
export interface IGlobalData {
  customParams: IPairedField[]
  globalParams: IPairedModule[]
  fieldsResource: IPairedModule[]
  multiSubWpInstParams: IPairedField[]
}

export type IWorkPackage = Omit<IWorkPackageResponse, 'wpRawData' | 'wpDesign'> & {
  wpDesign: IWPDesign
  wpRawData: IGraphData
}

// 表单类型
export type IFormType = 0 | 1 | 2 | 3

/**
 * 查询工作包信息
 * @param params
 * @returns
 */
export async function queryWorkPackage(params: { id: string }): Promise<IWorkPackage> {
  const { status, data } = await request<IResponse<IWorkPackageResponse>>({
    method: 'get',
    url: '/sys-bpm/wp',
    params: params
  })
  //   if (!status) return
  const { wpRawData: wpRawDataStr, wpDesign: wpDesignStr } = data
  // for 数据调整
  const wpDesign: IWPDesign = {
    ...JSON.parse(wpDesignStr),
    wpResources: JSON.parse(wpDesignStr).wpResource
  }
  const wpRawData: IGraphData = JSON.parse(wpRawDataStr)
  return {
    ...data,
    wpDesign: {
      ...wpDesign
    },
    wpRawData
  }
}
interface IStructure {
  graphData: IGraphData
  wpNodeInfoList: INodeInfo[]
}
export interface INodeInfo {
  nodeKey: string
  nodeProcessState: string
  nodeFormKey: string
  nodeName: string
  bizId: string
  wpKey: string
  triggerProcessInstanceId?: string
  wpProcessInstanceId?: string
  wpTaskId?: string
}
interface IStructureResponse {
  wpStructureInfo: IWorkPackageResponse
  wpNodeInfoList: INodeInfo[]
}
export async function queryWorkPackageStructure(params: { wpProcessInstanceId: string }): Promise<IStructure> {
  const { status, data } = await request<IResponse<IStructureResponse>>({
    method: 'get',
    url: '/sys-bpm/wp/monitor/structure',
    params: params
  })
  //   if (!status) return
  const { wpStructureInfo, wpNodeInfoList } = data
  const graphData: IGraphData = JSON.parse(wpStructureInfo.wpRawData)
  graphData.nodes = graphData.nodes.map((node) => {
    const unit = wpNodeInfoList.find((unit) => node.id === unit.nodeKey)
    if (unit) {
      node.properties.nodeProcessState = unit.nodeProcessState
    }
    return node
  })
  return {
    graphData,
    wpNodeInfoList
  }
}
interface ISubWpFormList {
  list: INodeInfo[]
  total: number
}
//查询子工作包内节点表单数据
export function querySubWpForm(params: { page: number; size: number; unitKey: string; wpProcessInstanceId: string }) {
  return request<IResponse<ISubWpFormList>>({
    url: '/sys-bpm/subWp/monitor',
    method: 'get',
    params
  })
}
export interface IMonitorData {
  wpName: string
  wpProcessInstanceId: string
  wpProcessState: string
  startDate: string
  finishDate: string
}
interface IMonitorList {
  list: IMonitorData[]
  total: number
}
//查询工作包监控列表数据
export function queryMonitorList(params: {
  page: number
  size: number
  column: string
  sort: string
  name: string
  state: string
  startDateBegin: string
  startDateEnd: string
  finishDateBegin: string
  finishDateEnd: string
}) {
  return request<IResponse<IMonitorList>>({
    url: '/sys-bpm/wp/monitor',
    method: 'get',
    params
  })
}
//查询登录用户的工作包列表数据
export function queryUserList(params: {
  page: number
  size: number
  column: string
  sort: string
  name: string
  state: string
  startDateBegin: string
  startDateEnd: string
  finishDateBegin: string
  finishDateEnd: string
}) {
  return request<IResponse<IMonitorList>>({
    url: '/sys-bpm/wp/monitor/user',
    method: 'get',
    params
  })
}
//删除工作包
export function deleteWorkPackage(params: { id: string }) {
  return request<IResponse>({
    url: '/sys-bpm/wp',
    method: 'delete',
    params
  })
}
//停用工作包
export function pauseWorkPackage(params: { wpKey: string }) {
  return request<IResponse>({
    url: '/sys-bpm/wp/pause',
    method: 'post',
    params
  })
}
//启用工作包
export function releaseWorkPackage(params: { id: string }) {
  return request<IResponse>({
    url: '/sys-bpm/wp/onRelease',
    method: 'post',
    params
  })
}
//查询登录用户的工作包列表数据
export function queryWpList(params: { name: string }) {
  return request<IResponse<IWorkPackageResponse[]>>({
    url: '/sys-bpm/wp/list',
    method: 'get',
    params
  })
}
export type IWorkPackageRequest = Omit<IWorkPackageResponse, 'wpDesign'> & {
  wpDesign: IWPDesign
}
//新增工作包
export function createWorkPackage(data: IWorkPackageRequest) {
  return request<IResponse<IWorkPackageResponse>>({
    url: '/sys-bpm/wp',
    method: 'post',
    data
  })
}
//更新工作包
export function updateWorkPackage(data: IWorkPackageRequest) {
  return request<IResponse<IWorkPackageResponse>>({
    url: '/sys-bpm/wp',
    method: 'put',
    data
  })
}

//近十次执行时间
export function queryTimerPreview(data: {
  endTime: string
  frequency: string
  frequencySpace: number
  frequencyWeeks: string[]
  startTime: string
}) {
  return request<IResponse<string[]>>({
    url: '/sys-bpm/wp/timer/preview',
    method: 'POST',
    data: {
      ...data,
      frequencyWeeks: data.frequencyWeeks.join(',')
    }
  })
}
interface IRejectParams {
  targetNode: string
  wpProcessInstanceId: string
}
//工作包监控功能-退回工作包到指定节点
export function retreatNode(data: IRejectParams) {
  return request<IResponse>({
    url: '/sys-bpm/wp/monitor/retreat',
    method: 'post',
    params: data
  })
}
//工作包监控功能-终止工作包
export function abandonWp(wpProcessInstanceId: string) {
  return request<IResponse>({
    url: '/sys-bpm/wp/monitor/termination',
    method: 'post',
    params: { wpProcessInstanceId }
  })
}
//工作包监控功能-重新发起工作包
export function restartWp(wpProcessInstanceId: string) {
  return request<IResponse>({
    url: '/sys-bpm/wp/monitor/restart',
    method: 'post',
    params: { wpProcessInstanceId }
  })
}
