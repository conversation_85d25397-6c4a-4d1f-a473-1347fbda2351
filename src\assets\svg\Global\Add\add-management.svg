<?xml version="1.0" encoding="UTF-8"?>
<svg width="64px" height="64px" viewBox="0 0 64 64" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>添加</title>
    <defs>
        <circle id="path-1" cx="32" cy="32" r="32"></circle>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="64" height="64" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
    </defs>
    <g id="应用管理主页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="01应用管理-3待发布" transform="translate(-260.000000, -221.000000)">
            <g id="待发布" transform="translate(248.000000, 176.000000)">
                <g id="添加应用02" transform="translate(2.000000, 45.000000)">
                    <g id="添加" transform="translate(10.000000, 0.000000)">
                        <use id="椭圆形" stroke="#CFD3D7" mask="url(#mask-2)" stroke-width="2" fill="#EFF0F5" stroke-dasharray="5,4" xlink:href="#path-1"></use>
                        <path d="M32,22 C32.7363797,22 33.3333333,22.5969537 33.3333333,23.3333333 L33.333,30.666 L40.6666667,30.6666667 C41.4030463,30.6666667 42,31.2636203 42,32 C42,32.7363797 41.4030463,33.3333333 40.6666667,33.3333333 L33.333,33.333 L33.3333333,40.6666667 C33.3333333,41.4030463 32.7363797,42 32,42 C31.2636203,42 30.6666667,41.4030463 30.6666667,40.6666667 L30.666,33.333 L23.3333333,33.3333333 C22.5969537,33.3333333 22,32.7363797 22,32 C22,31.2636203 22.5969537,30.6666667 23.3333333,30.6666667 L30.666,30.666 L30.6666667,23.3333333 C30.6666667,22.5969537 31.2636203,22 32,22 Z" id="形状结合" fill="#9CA6AF" opacity="0.7"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>