<?xml version="1.0" encoding="UTF-8"?>
<svg width="120px" height="80px" viewBox="0 0 120 80" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>矩形 + 矩形备份蒙版备份</title>
    <defs>
        <polygon id="path-1" points="0 0 120 0 120 80 0 80"></polygon>
        <path d="M22.5638852,20 L137.436115,20 C138.327634,20 138.65092,20.0928256 138.976846,20.2671327 C139.302772,20.4414398 139.55856,20.6972284 139.732867,21.0231543 C139.907174,21.3490802 140,21.6723665 140,22.5638852 L140,97.4361148 C140,98.3276335 139.907174,98.6509198 139.732867,98.9768457 C139.55856,99.3027716 139.302772,99.5585602 138.976846,99.7328673 C138.65092,99.9071744 138.327634,100 137.436115,100 L22.5638852,100 C21.6723665,100 21.3490802,99.9071744 21.0231543,99.7328673 C20.6972284,99.5585602 20.4414398,99.3027716 20.2671327,98.9768457 C20.0928256,98.6509198 20,98.3276335 20,97.4361148 L20,22.5638852 C20,21.6723665 20.0928256,21.3490802 20.2671327,21.0231543 C20.4414398,20.6972284 20.6972284,20.4414398 21.0231543,20.2671327 C21.3490802,20.0928256 21.6723665,20 22.5638852,20 Z" id="path-3"></path>
        <filter x="-13.3%" y="-17.5%" width="126.7%" height="140.0%" filterUnits="objectBoundingBox" id="filter-4">
            <feOffset dx="0" dy="2" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.0898254103   0 0 0 0 0.115558755   0 0 0 0 0.227270154  0 0 0 0.210473121 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <path d="M58.5638852,40 L173.436115,40 C174.327634,40 174.65092,40.0928256 174.976846,40.2671327 C175.302772,40.4414398 175.55856,40.6972284 175.732867,41.0231543 C175.907174,41.3490802 176,41.6723665 176,42.5638852 L176,117.436115 C176,118.327634 175.907174,118.65092 175.732867,118.976846 C175.55856,119.302772 175.302772,119.55856 174.976846,119.732867 C174.65092,119.907174 174.327634,120 173.436115,120 L58.5638852,120 C57.6723665,120 57.3490802,119.907174 57.0231543,119.732867 C56.6972284,119.55856 56.4414398,119.302772 56.2671327,118.976846 C56.0928256,118.65092 56,118.327634 56,117.436115 L56,42.5638852 C56,41.6723665 56.0928256,41.3490802 56.2671327,41.0231543 C56.4414398,40.6972284 56.6972284,40.4414398 57.0231543,40.2671327 C57.3490802,40.0928256 57.6723665,40 58.5638852,40 Z" id="path-5"></path>
        <filter x="-4.2%" y="-5.0%" width="108.3%" height="112.5%" filterUnits="objectBoundingBox" id="filter-6">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.0898254103   0 0 0 0 0.115558755   0 0 0 0 0.227270154  0 0 0 0.210473121 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="正式版" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="Ant-Design-5.0-官网-PC" transform="translate(-726.000000, -1542.000000)">
            <g id="编组-28" transform="translate(506.000000, 1542.000000)">
                <g id="矩形-+-矩形备份蒙版备份" transform="translate(220.000000, 0.000000)">
                    <mask id="mask-2" fill="white">
                        <use xlink:href="#path-1"></use>
                    </mask>
                    <use id="蒙版" fill="#4F5155" xlink:href="#path-1"></use>
                    <g id="矩形" mask="url(#mask-2)">
                        <use fill="black" fill-opacity="1" filter="url(#filter-4)" xlink:href="#path-3"></use>
                        <use fill="#292929" fill-rule="evenodd" xlink:href="#path-3"></use>
                    </g>
                    <g id="矩形备份" mask="url(#mask-2)">
                        <use fill="black" fill-opacity="1" filter="url(#filter-6)" xlink:href="#path-5"></use>
                        <use fill="#4F5155" fill-rule="evenodd" xlink:href="#path-5"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>