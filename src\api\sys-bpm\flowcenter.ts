import { request } from '@/fawkes'
import type { IResponse, IPageResponse } from '@/types/'
export interface IQueryCirculation {
  senderUserFullName: string
  title: string
  type: string
  order: string
  startTime: string
  endTime: string
  status: string
  column: string
  page: number
  size: number
}
export interface ICirculationItem {
  createBy: string
  createDate: string
  deleteFlag: number
  formBizId: string
  formKey: string
  id: string
  processInstanceId: string
  processType: string
  receiverUserFullName: string
  receiverUserName: string
  senderUserFullName: string
  senderUserName: string
  sourceId: null | string
  sourceName: null | string
  status: string
  taskId: string
  taskKey: string
  tenantId: number
  title: string
  updateBy: string
  updateDate: string
}
// 查看抄送列表
export function queryCirculationData(params: IQueryCirculation) {
  return request<IPageResponse<ICirculationItem>>({
    method: 'get',
    url: '/sys-bpm/circulation/page',
    params: params
  })
}
export interface ICirculationForm {
  title: string
  senderUserFullName: string
  senderUserName: string
  receiverUserName: string
  receiverUserFullName: string
  notify: string[]
}
export type ICreateCirculation = {
  notify: string
  formBizId: string
  formKey: string
  taskId: string
  processInstanceId: string
  taskKey: string
  processType: string
} & Omit<ICirculationForm, 'notify'>
// 发起抄送
export function createCirculation(data: ICreateCirculation) {
  return request<IResponse>({
    method: 'POST',
    url: '/sys-bpm/circulation',
    data: data
  })
}

export interface IQueryTask {
  asignee?: string
  creator?: string
  formName: string
  startDate: string
  endDate: string
  processStartDate?: string
  processEndDate?: string
  taskSubject?: string
  page: number
  order: string
  column: string
  size: number
  taskState?: string
  taskListState?: number
}
export interface ITaskItem {
  appId: string
  appName: string
  browseFlag: string
  createBy: null | string
  createDate: string
  currentState: string
  customId: null | string
  customTitle: null | string
  deleteFlag: null | number
  dueDate: null | string
  ext1?: null | string
  ext2?: null | string
  ext3?: null | string
  ext4?: null | string
  ext5?: null | string
  finishDate: string
  formBizId: string
  formKey: string
  formName: string
  id: string
  initialTaskId: null | string
  isHide: string
  parallelFullnameList: null | string[]
  parallelList: null | string[]
  pauseFlag: string
  portalId: string
  processCreateDate: string
  processInstanceId: string
  processState: string
  processType?: string
  sort: string
  taskAsignee: string
  taskAsigneeName: string
  taskCreator: string
  taskCreatorName: string
  taskId: string
  taskKey: string
  taskName: string
  taskState: string
  taskSubject: string
  taskType: null | string
  tenantId: number
  updateBy: null | string
  updateDate: null | string
  url: null | string
}
//查询我的任务列表
export function queryMyTaskList(params: IQueryTask) {
  return request<IPageResponse<ITaskItem>>({
    method: 'get',
    url: '/sys-bpm/v2/createTasks',
    params: params
  })
}
//查询已完成任务列表
export function queryCompletedList(params: IQueryTask) {
  return request<IPageResponse<ITaskItem>>({
    url: '/sys-bpm/relTasks/completed',
    method: 'get',
    params: params
  })
}
//撤回任务
export function withdrawTask(taskId: string) {
  return request<IResponse>({
    url: '/sys-bpm/process/withdraw',
    method: 'put',
    params: { taskId }
  })
}
export interface IQueryProcess {
  formName: string
  creator: string
  startDate: string
  endDate: string
  taskState: null | number
  page: number
  size: number
  order: string
  column: string
}
//查询流程监控列表
export function queryProcessData(data: IQueryProcess) {
  return request<IPageResponse<ITaskItem>>({
    method: 'get',
    url: '/sys-bpm/relTasks/all',
    params: data
  })
}
//查询流程任务节点
export function queryTaskNode(taskId: string) {
  return request({
    method: 'get',
    url: '/sys-bpm/process/taskKey',
    params: {
      taskId: taskId
    }
  })
}
export interface IRejectFlow {
  taskId: string
  targetKey: string
  comment: null | string
}
//
export function rejectFlow(params: IRejectFlow) {
  return request<IResponse>({
    url: '/sys-bpm/process/reject',
    method: 'PUT',
    params: params,
    data: JSON.stringify({}),
    transformRequest: [
      (data) => {
        return data
      }
    ],
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
// 废弃流程
export function abandonProcess(taskId: string) {
  return request<IResponse>({
    method: 'delete',
    url: 'sys-bpm/process',
    params: {
      taskId
    }
  })
}
export interface IProcessHistory {
  approveDate: string
  approveState: string
  approveStateName: string
  assignee: string
  assigneeName: string
  comment: string
  createBy: string | null
  createDate: string
  delegateComment: string | null
  delegateDueDate: string | null
  delegateFileToken: string | null
  delegateOrgName: string | null
  delegateOrgNo: string | null
  delegateUser: string | null
  delegateUserName: string | null
  deleteFlag: number | null
  ext1: string | null
  ext2: null
  ext3: null
  ext4: null
  ext5: null
  fileToken: null | string
  formBizId: string
  id: string
  orgName: string | null
  orgNo: string | null
  portalId: string
  processCreateDate: string
  processDefId: string
  processInstanceId: string
  processState: string
  taskId: string
  taskKey: string
  taskName: string
  taskState: string
  taskSubject: string
  tenantId: number
  updateBy: null | string
  updateDate: null | string
}
// 获取流程评论,用于展示评论列表
export function queryProcessHistory(processInstanceId: string) {
  return request<IResponse<IProcessHistory[]>>({
    method: 'get',
    url: '/sys-bpm/process/history',
    params: {
      processInstanceId
    }
  })
}
export interface IQueryHistoryNodes {
  currentNode: string[]
  endEvent: string | null
  exclusiveGateway: string[]
  inclusiveGateway: string[]
  parallelGateway: string[]
  sequenceFlow: string[]
  startEvent: string | null
  userTask: string[]
}
// 获取流程进行时的流程信息，用于展示流程图
export async function queryHistoryNodes(processInstanceId: string): Promise<IQueryHistoryNodes> {
  const { data } = await request<IResponse<IQueryHistoryNodes>>({
    method: 'get',
    url: '/sys-bpm/process/history/instance',
    params: { processInstanceId }
  })
  return data
}
export interface IQueryModel {
  bpmnXml: string
  createBy: string
  createDate: string
  customProcessJson: string | null
  deleteFlag: number
  deployVersion: number
  id: string
  isDeploy: string
  modelKey: string
  modelName: string
  modelType: string | null
  processType: string
  projectId: string | null
  tenantId: number
  updateBy: string
  updateDate: string
  version: number
}
//  获取流程图xml
export async function queryModel(processInstanceId: string): Promise<IQueryModel> {
  const { data } = await request<IResponse<IQueryModel>>({
    method: 'get',
    url: '/sys-bpm/model',
    params: {
      processInstanceId: processInstanceId
    }
  })
  return data
}
//  查询任务待办详情
// export function queryUserTaskDetail(taskId: string) {
//   return request({
//     url: '/sys-bpm/userTask',
//     method: 'get',
//     params: {
//       taskId: taskId
//     }
//   })
// }
export interface IBatchDeleteProcess {
  batchDeleteProcessParam: IBatchDeleteProcessParams
  comment?: string
}
interface IBatchDeleteProcessParams {
  taskIdList?: string[]
  bizIdList?: string[]
}
//批量废弃
export function batchDeleteProcess(data: IBatchDeleteProcess) {
  return request<IResponse>({
    url: '/sys-bpm/process/delete/batch',
    method: 'post',
    params: {
      comment: data.comment
    },
    data: data.batchDeleteProcessParam
  })
}
export interface IUrgeTask {
  taskId: string
  notify: string
}
//催办
export function urgeTask(params: IUrgeTask) {
  return request<IResponse>({
    method: 'post',
    url: '/sys-bpm/process/urge',
    params: params
  })
}
export interface ICount {
  all: number
  expired: number
  expiring: number
  staging: number
  urgent: number
}
//统计任务个数
export function queryCount() {
  return request<IResponse<ICount>>({
    url: '/sys-bpm/userTask/count',
    method: 'get'
  })
}
//查询我的待办任务列表
export function queryUserTask(data: IQueryTask) {
  return request<IPageResponse<ITaskItem>>({
    method: 'get',
    url: '/sys-bpm/userTasks',
    params: { ...data }
  })
}
//查询任务管理列表
export function queryRelTaskData(params: IQueryTask) {
  return request<IPageResponse<ITaskItem>>({
    method: 'get',
    url: '/sys-bpm/userTask/manage',
    params: params
  })
}
export interface IConsulationForm {
  userNames: string
  userFullnames: string
  comment: string
}
export type IConsulationParams = {
  taskId: string
} & Pick<IConsulationForm, 'userNames' | 'comment'>
export function createConsultation(params: IConsulationParams) {
  return request<IResponse>({
    url: '/sys-bpm/process/consultation/add',
    method: 'put',
    params: params
  })
}
// 查看抄送，更新查阅状态
export function updateStatus(params: { id: string }) {
  return request<IResponse<null>>({
    method: 'get',
    url: '/sys-bpm/circulation',
    params
  })
}