// function broadcast(componentName, eventName, params) {
//   this.$children.forEach(child => {
//     var name = child.$options.componentName;

//     if (name === componentName) {
//       child.$emit.apply(child, [eventName].concat(params));
//     } else {
//       broadcast.apply(child, [componentName, eventName].concat([params]));
//     }
//   });
// }
export function dispatch(that, componentName, eventName, params?: any[]) {
  let parent = that.$parent || that.$root
  let name = parent.$options.componentName

  while (parent && (!name || name !== componentName)) {
    parent = parent.$parent

    if (parent) {
      name = parent.$options.componentName
    }
  }
  if (parent) {
    parent.$emit.apply(parent, [eventName].concat(params))
  }
}
