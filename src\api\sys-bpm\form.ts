import { request } from '@/fawkes'
import { IResponse, IPageResponse } from '@/types'
import { IApproval } from '@/modules/FormCenter/componentsWp/CustomAssignee/type'
import { INodeConfig } from '@/modules/FormCenter/componentsWp/customFlowDiagram/type'
import { ICommentItem, ITaskInfo } from '@/modules/FormCenter/componentsWp/Form/type'
import { INode, ISubmitFormPermission } from '../sys-form/appModuleDesigner.type'
//抄送
interface ICirculation {
  title: string
  senderUserFullName: string
  senderUserName: string
  receiverUserName: string
  receiverUserFullName: string
  notify: string
  formBizId: string
  formKey: string
  taskId: string
  processInstanceId: string
  taskKey: string
  processType: string
}

interface IQueryCirculationListParam {
  senderUserFullName: string
  title: string
  type: string
  order: string
  startTime: string
  endTime: string
  status: string
  column: string
  page: number
  size: number
}
// 查看抄送列表
export function queryCirculationList(params: IQueryCirculationListParam) {
  return request<IPageResponse<ICirculation>>({
    method: 'get',
    url: '/sys-bpm/circulation/page',
    params
  })
}

// 发起抄送
export function createCirculation(data: ICirculation) {
  return request<IResponse<null>>({
    method: 'POST',
    url: '/sys-bpm/circulation',
    data
  })
}

//委托
interface ICreateDalegateParam {
  taskId: string
  comment: string
  userName: string
}

//发起委托
export function createDelegate(params: ICreateDalegateParam) {
  return request<IResponse<null>>({
    method: 'put',
    url: '/sys-bpm/process/delegate',
    params
  })
}

interface ICreateConsultationParam {
  taskId: string
  userNames: string
  comment: string
}

//征询
export function createConsultation(params: ICreateConsultationParam) {
  return request<IResponse<null>>({
    url: '/sys-bpm/process/consultation/add',
    method: 'put',
    params: params
  })
}

//自由流程
interface IQueryApprovalConfigParam {
  modelKey: string
  processInstanceId: string
}

// 获取自由流程节点审批人相关信息
export function queryApprovalList(params: IQueryApprovalConfigParam) {
  return request<IResponse<IApproval[]>>({
    url: '/sys-bpm/custom/node/config',
    method: 'get',
    params
  })
}

interface IQueryApprovalDetailParam {
  bizId: string
  processInstanceId: string
}

// 获取流程节点审批数据
export function queryCustomNodeDetail(params: IQueryApprovalDetailParam) {
  return request<IResponse<IApproval[]>>({
    url: '/sys-bpm/custom/node/detail',
    method: 'get',
    params: params
  })
}

interface IQueryProcessHistoryParam {
  taskId: string
  bizId: string
}

/**
 * @description: 获取流程评论
 * @param {data}
 */
export function queryProcessHistory(params: IQueryProcessHistoryParam) {
  return request<IResponse<ICommentItem[]>>({
    method: 'get',
    url: '/sys-bpm/process/history',
    params
  })
}

//流程图
interface IQueryHistoryNodesParam {
  taskId: string
  bizId: string
}

export interface IHistoryNodes {
  currentNode: string[]
  endEvent: string
  exclusiveGateway: string[]
  inclusiveGateway: string[]
  parallelGateway: string[]
  sequenceFlow: string[]
  startEvent: string
  userTask: string[]
}

//获取历史节点
export function queryHistoryNodes(params: IQueryHistoryNodesParam) {
  return request<IResponse<IHistoryNodes>>({
    method: 'get',
    url: '/sys-bpm/process/history/instance',
    params
  })
}

interface ICustomXML {
  bpmnXml: string
  createBy: string
  createDate: string
  customProcessJson: string
  deleteFlag: number
  deployVersion: number
  id: string
  isDeploy: string
  modelKey: string
  modelName: string
  modelType: string
  processType: string
  projectId: null
  tenantId: number
  updateBy: string
  updateDate: string
  version: number
}

// 获取XML后提取自由流程节点审批人相关信息
export async function queryCustomXML(modelKey: string): Promise<INode[]> {
  const { data } = await request<IResponse<ICustomXML>>({
    url: '/sys-bpm/model',
    method: 'get',
    params: {
      modelKey
    }
  })
  return JSON.parse(data.customProcessJson).nodeList
}

interface IQueryNodePermissionParam {
  modelKey: string
  taskKey: string
  taskId: string
}

export interface INodePermission {
  detailPermission: ISubmitFormPermission
  formPermission: ISubmitFormPermission
}

// 自定义表单  查询当前节点表单字段权限信息
export function queryNodePermission(params: IQueryNodePermissionParam) {
  return request<IResponse<INodePermission>>({
    url: '/sys-bpm/process/formPerssion',
    method: 'get',
    params
  })
}

//查询任务待办详情
export function queryTaskInfo(taskId: string) {
  return request<IResponse<ITaskInfo>>({
    url: '/sys-bpm/userTask',
    method: 'get',
    params: {
      taskId
    }
  })
}
