import { request } from '@/fawkes'
import { IPageResponse, IResponse } from '@/types'
import { AxiosResponse } from 'axios'
import { IRegisterParam, IQueryUsersPageParam } from './index'
export interface IOrgTree {
  id?: string
  parentId?: string
  content: IOrgTreeContent
  children?: IOrgTree[]
}
export interface IOrgTreeContent {
  entName: string
  id: string
  memberNum: string
  name: string
  orgNo: string
  orgType: null
  parentName: string
  parentNo: number
  pathName: string
  pathNo: string
  portalId: string
  sort: number
  tenantId: number
  type: number
}
//获取部门树
export function queryOrgTree(portalId: string) {
  return request<IResponse<IOrgTree[]>>({
    url: '/sys-user/orgs/tree',
    method: 'get',
    params: {
      isAll: false,
      portalId: portalId,
      needMemberNum: true
    }
  })
}
//导出部门
export function exportOrg(data: string[]) {
  return request<AxiosResponse<Blob>>({
    url: '/sys-user/orgs/export',
    method: 'post',
    responseType: 'blob',
    data: data
  })
}
//删除
export function deleteOrg(id: string) {
  return request<IResponse>({
    url: '/sys-user/org',
    method: 'delete',
    params: { id }
  })
}
type IQueryUsersPageParamV2 = IQueryUsersPageParam & { postId?: string }
//获取组织机构下人员分页
export function queryUsersPageV2(params: IQueryUsersPageParamV2) {
  return request<IPageResponse<IRegisterParam>>({
    url: '/sys-user/v2/users/page',
    method: 'get',
    params: params
  })
}
interface IUpdatePortalParams {
  id: string
  addUserIdList?: string[]
  deleteUserIdList?: string[]
}
//更新门户
export function updatePortal(data: IUpdatePortalParams) {
  return request<IResponse>({
    url: '/sys-user/portal',
    method: 'put',
    data: data
  })
}
interface IUpdateOrgMemberParams {
  addUserIdList: string[]
  deleteUserIdList: string[]
  portalId: string
  orgNo: string
}
// 更新组织下成员
export function updateOrgMember(data: IUpdateOrgMemberParams) {
  return request<IResponse>({
    url: '/sys-user/org/users',
    method: 'put',
    data: data
  })
}
export interface IDeptForm {
  name: string
  entName: string
  parentNo?: string
  parentName?: string
  pathName?: string
  pathNo?: string
}
export interface IPortalInfo {
  portalId: string
  type: number
}
//修改部门
export function updateOrg(data: IOrgTreeContent & IPortalInfo) {
  return request<IResponse>({
    url: '/sys-user/org',
    method: 'put',
    data: data
  })
}
//新增部门
export function createOrg(data: IDeptForm & IPortalInfo) {
  return request<IResponse>({
    url: '/sys-user/org',
    method: 'post',
    data: data
  })
}

export interface IPostItem {
  id: string
  isGeneral: boolean
  memberNum: string
  portalId: string
  postCode: string
  postName: string
  postType: string
  remark: string
  sort: string
  tenantId: number
  type: number
}

export interface IPostPageParams {
  pageNo: number
  pageSize: number
  searchValue: string
  portalId: string
  sort: string
  columnName: string
  needMemberNum: boolean
}
//获取岗位分页
export function queryPostListPage(params: IPostPageParams) {
  return request<IPageResponse<IPostItem>>({
    url: '/sys-user/post/page',
    method: 'get',
    params: params
  })
}
//导出岗位
export function exportPost(data: string[], flag: boolean) {
  return request<AxiosResponse<Blob>>({
    url: '/sys-user/posts/export',
    method: 'post',
    responseType: 'blob',
    data: data,
    params: { exportAll: flag }
  })
}
//根据id删除岗位
export function deletePost(id: string) {
  return request<IResponse>({
    url: '/sys-user/post',
    method: 'DELETE',
    params: {
      id: id
    }
  })
}
interface IQueryPostUser {
  pageNo: number
  pageSize: number
  searchValue: string
  postId: string
  portalId: string
  sort: string
}
//获取岗位下用户
export function queryRoleGroupUser(params: IQueryPostUser) {
  return request<IPageResponse<IRegisterParam>>({
    url: '/sys-user/post/users/page',
    method: 'get',
    params: {
      ...params
    }
  })
}
interface IUpdatePostParams {
  addUserIdList: string[]
  deleteUserIdList: string[]
  portalId?: string
  type?: number | string
  id: string
}
/** 更新岗位 */
export function updatePost(data: IUpdatePostParams | IPostItem) {
  return request<IResponse>({
    url: '/sys-user/post',
    method: 'PUT',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export interface IPostForm {
  postName: string
  remark: string
}
//新增岗位
export function createPost(data: IPostForm & IPortalInfo) {
  return request<IResponse>({
    url: '/sys-user/post',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export interface IExportPeopleFileParams {
  accountStatus: number
  exportAll: boolean
  portalId: string
  filterHaveOrg?: boolean
  filterNoOrg?: boolean
  orgNo?: string
}
//导出人员
export function exportPeopleFile(data: IExportPeopleFileParams) {
  return request<AxiosResponse<Blob>>({
    url: '/sys-user/users/export',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
//更新用户
export function updateUser(data: IRegisterParam) {
  return request<IResponse>({
    url: '/sys-user/user',
    method: 'put',
    data: JSON.stringify(data),
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
export type ICreateUserParams = {
  deptId?: string //部门id
  deptName?: string //部门名称
  orgList?: IOrgTreeContent[] | string[] //部门id集合
  postList?: string[] //岗位id集合
} & IRegisterParam
//新增用户
export function createUser(data: ICreateUserParams) {
  return request<IResponse<IRegisterParam>>({
    url: '/sys-user/user',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  })
}
//获取岗位列表
export function queryPostList(portalId: string) {
  return request<IResponse<IPostItem[]>>({
    url: '/sys-user/posts',
    method: 'get',
    params: {
      portalId: portalId
    }
  })
}
