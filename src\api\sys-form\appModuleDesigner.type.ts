/*
 *模块设计整体结构
 */
import { ITreeNode } from '@/types'
import { ITableColumn } from '@/modules/AppCenter/AppModulesDesigner/FormGenerator/RightPanel/DetailTableConfig/type'
import type {
  ICurrentFormField,
  ICheckedRelateFormField
} from '@/modules/AppCenter/AppModulesDesigner/FormGenerator/RightPanel/AssociateDataConfig/type'
import { IRelateInputParamsItem } from './connector'
import { IConnectorProperties } from '../sys-bpm/workpackage'
export interface IModule {
  formDesign: IFormDesign
  processDesign: IProcessDesign
  tableDesign: ITableDesign
  authDesign: IAuthDesign
  extraDesign: IExtraDesign
  publishDesign: IPublishDesign
  id?: string
}
export interface IEditBoxForm {
  editBoxHtml: string
  editBoxValue: string
}
export interface IFileItem {
  name: string
  status?: string
  token: string
  uid?: number
  url: string
}
export type IComponentDesc = IEditBoxForm | IFileItem[] | string
export interface IFormDesign {
  formKey?: string
  formType?: EFormType
  groupId?: string
  formName: string
  fields: IComponent[]
}
export type IFormType = '0' | '1' | '2'
export enum EFormType {
  PROCESS = '0',
  COMMON = '1',
  PULIC = '2'
}
type ITag =
  | 'fks-input'
  | 'fks-amount'
  | 'fks-input-number'
  | 'fks-select'
  | 'fks-cascader'
  | 'address-selector'
  | 'fks-radio-group'
  | 'fks-checkbox-group'
  | 'fks-switch'
  | 'fks-slider'
  | 'fks-time-picker'
  | 'fks-date-picker'
  | 'fks-rate'
  | 'fks-color-picker'
  | 'form-upload'
  | 'form-divider'
  | 'form-number'
  | 'wang-editor'
  | 'fks-user-selector-pro'
  | 'fks-org-selector-pro'
  | 'detail-table'
  | 'fks-button'
  | 'wp-tabs'
  | 'formula'
  | 'ocr'
  | 'write-info'
  | 'directory-tree'
  | 'administrative'
  | 'relate-cascader'
  | 'region'

export type ITagAttribute =
  | 'input'
  | 'comment'
  | 'count'
  | 'amount'
  | 'number'
  | 'select'
  | 'cascader'
  | 'address-selector'
  | 'radio'
  | 'check-box'
  | 'form_switch'
  | 'form_slider'
  | 'time'
  | 'time-range'
  | 'date'
  | 'date-range'
  | 'rate'
  | 'color'
  | 'upload'
  | 'form-divider'
  | 'form-number'
  | 'wang-editor'
  | 'user-selector'
  | 'org-selector'
  | 'detail-table'
  | 'associate-data'
  | 'wp-tabs'
  | 'formula'
  | 'ocr'
  | 'information'
  | 'notify'
  | 'directory-tree'
  | 'switch'
  | 'ethnicity'
  | 'owner-input'
  | 'custom-formula'
  | 'administrative'
  | 'relate-cascader'
  | 'region'
// 'owner-input' 为环节表单的隐藏组件
// 'ethnicity' 民族选择器
// 'administrative' 项目行政区划
// 'relate-cascader' 跨组件级联
// 'region' 项目所属区域

export type IDataType = 'string' | 'number' | 'time'

export interface IRegItem {
  type: 'none' | 'mobilePhone' | 'email' | 'IDNumber' | 'postcode' | 'custom' //正则类型
  pattern: string // 正则表达式
  message: string //提示文字
}
export type IComponent =
  | IBasicComponent
  | IInput
  | ISelect
  | IDivider
  | IComment
  | IRate
  | IColor
  | IDetailTable
  | ISwitch
  | ICascader
  | IUserSelector
  | IOrgSelector
  | ISlider
  | IWpTabs
  | ICount
  | IDateAndTimeRange
  | IDateAndTime
  | IFormNumber
  | IFormula
  | IAmount
  | ICheckBox
  | IRadio
  | IAssociateData
  | IAddress
  | IOcr
  | INumber
  | IDirectoryTree
  | IUpload
  | IEthnicity
  | IRelatedCascader

interface IDataConfig {
  filterCondition: IBusinessRuleItem[]
  matchingParams: { sourceField: string; targetField: string; targetFieldAttr: ITagAttribute }[]
  relateForm: string
  relateFormFields: string
  relateInputParams?: IRelateInputParamsItem[]
  type?: 'form' | 'connector'
  checkedOutputParams?: ICheckedRelateFormField[]
}

interface IBasicComponent {
  fieldId?: number
  parentType?: 'wp-tabs' | undefined
  parentId?: number
  __config__: IConfig
  __vModel__?: string
  placeholder?: string //提示文字
  clearable?: boolean //是否可清空
  maxlength?: number //最大长度
  'show-word-limit'?: boolean //显示限制长度
  readonly?: boolean //是否只读
  disabled?: boolean //是否禁用
  componentDesc?: IComponentDesc //组件介绍
  descType?: 'text' | 'richText' | 'attachment' //组件介绍的类型
  type?: string //input原生属性type textarea input date等
  multiple?: boolean //是否多选
  min?: number
  max?: number
  visibleControl?: IVisibleControl // 是否开启显隐控制
}
interface IVisibleControl {
  open: boolean // 是否开启显隐控制
  result: 'show' | 'hide' // 控制效果为 显示/隐藏
  type: 'OR' | 'AND' // 控制类型 且/或
  conditions: any[] // 控制条件
}
export interface IInput extends IBasicComponent {
  __slot__: { prepend: string; append: string }
  regList: IRegItem[] //正则校验，只有input和comment类型组件才有
}
export interface ISelect extends IBasicComponent {
  filterable: boolean //是否可搜索
  optionContentType: string
  __slot__: {
    options: {
      label: string
      value: string
    }[]
  } //下拉选择或者级联选择选项
  dataConfig: IDataConfig
}
export interface IDivider extends IBasicComponent {
  dividerText: string //分割线组件独有
  dividerDescribe: string //分割线组件独有
  dividerColor: string //分割线颜色 ，分割线组件独有
  titleColor: string //标题颜色 ，分割线组件独有
  titleBacColor: string //标题背景颜色，分割线组件独有
  activeConfig: string //标题背景颜色，分割线组件独有
}
export interface IComment extends IBasicComponent {
  __slot__: { prepend: string; append: string }
  autosize: {
    //多行文本独有属性
    minRows: number
    maxRows: number
  }
  regList: IRegItem[] //正则校验，只有input和comment类型组件才有
}
export interface IRate extends IBasicComponent {
  texts: string[] //輔助文字，评分组件独有
  'allow-half': boolean //是否允许半选，评分组件独有
  'show-text': boolean //是否显示辅助文字,评分组件独有
  'show-score': boolean //是否显示分数,评分组件独有
}
export interface IColor extends IBasicComponent {
  'show-alpha': boolean //颜色格式 颜色组件独有
  'color-format': string //颜色格式 颜色组件独有
  size: 'medium' | 'small' | 'mini' //按鈕大小
}
export interface IUpload extends IBasicComponent {
  limit: number //最大允许上传个数
  accept: string[] // 接受上传的文件类型 ['pdf','txt',...]
}
export interface IDetailTable extends IBasicComponent {
  tableColumn: ITableColumn[] // 明细表列
  tableName: string //明细表表名
  showSummary: boolean //显示汇总 //明细表独有
  showRowSummary: boolean //显示行汇总，明细表独有
  fold: boolean //折叠明细表
}
export interface ISwitch extends IBasicComponent {
  'active-text': string //开启文字，开关组件独有
  'inactive-text': string //关闭文字，开关组件独有
  'active-color': string //开启颜色，开关组件独有
  'inactive-color': string //关闭颜色，开关组件独有
  'active-value': string //开启值
  'inactive-value': string //关闭值
}
export interface ICascader extends IBasicComponent {
  filterable: boolean
  options: any[] //级联选择选项
  props: any //级联选择独有
  'show-all-levels': boolean //显示全部级别,级联选择独有
  separator: string //级联选择分隔符
  url: string // 级联选择独有，数据来源地址
  method: string //级联选择独有，请求方式
  optionContentType: 'custom' | 'associate' //自定义/选择连接器
  dataConfig: IDataConfig //连接器相关配置
}
export interface IRelatedCascader extends IBasicComponent {
  filterable: boolean
  props: any //级联选择独有
  'show-all-levels': boolean //显示全部级别,级联选择独有
  separator: string //级联选择分隔符
  source: {
    label: string
    dataType: string
    __vModel__: string
  } // 源组件
  conditions: {
    value: any
    options: any[]
  }[]
}
export interface IUserSelector extends IBasicComponent {
  urlPath: string //选人组件
  orgPath: string
  recentUser: boolean
  multipleLimit: number //选择数量上限
  multiPortal: boolean //多门户
  collapseTags: boolean //折叠标签
  setValue: string //选人组件
  setDefaultValue: string //选人组件
}
export interface IOrgSelector extends IBasicComponent {
  urlPath: string //选部门组件
  multiPortal: boolean //多门户
  collapseTags: boolean //折叠标签
  multipleLimit: unknown
  width: string //选择框宽度
}
export interface ISlider extends IBasicComponent {
  range: boolean //选择范围，滑块组件独有
  'show-stops': boolean //显示间断点,滑块组件独有
  step: number //步进
  // precision: number //精度 //计数器组件独有
}
export interface INumber extends IBasicComponent {
  __slot__: { prepend: string; append: string }
  limit: number // 限制
  thousandth: boolean // 是否显示千分位
}
export interface ICount extends IBasicComponent {
  step: number //步进
  precision: number //精度 //计数器组件独有
  'controls-position': string //按钮位置计数器组件独有
}
export interface IWpTabs extends IBasicComponent {
  tabs: ITab[] //wp-tabs组件独有
}
export interface ITab {
  id: number
  name: string
  label: string
  fields: IComponent[]
}
export interface IDateAndTimeRange extends IBasicComponent {
  'is-range': boolean // 是否是时间或日期范围
  separator: string //连接分隔符
  'start-placeholder': string //开始时间占位符
  'end-placeholder': string //结束时间占位符
  'value-format': string //时间值格式
  format: string //时间格式
}
export interface IDateAndTime extends IBasicComponent {
  'value-format': string //时间值格式
  format: string //时间格式

  'picker-options'?: {
    //时间组件独有
    [key: string]: any
  }
}
export interface IFormNumber extends IBasicComponent {
  numberConfig: any[] //表单编号组件独有
  numberFormat: 1 | 2 //编号格式 ，表单编号组件独有
}
export interface IFormula extends IBasicComponent {
  formula: {
    [key: string]: any
  }
  __slot__: {
    prepend: string
    append: string
  }
  limit: number
  thousandth: boolean
  format: string
  'picker-options': {
    selectableRange: string
  }
  'value-format': string
}

export interface ICustomFormula extends IBasicComponent {
  __slot__: {
    prepend: string
    append: string
  }
  limit: number
  thousandth: boolean
  formulaStr: string
  variableMap: any[]
}
export interface IAmount extends IBasicComponent {
  limit: number //金额或数字组件保留小数位数
  thousandth: boolean //是否开启千分位，金额或数字组件独有
  __slot__: { prepend: string; append: string; activeSlot: 'prepend' | 'append' }
}
export interface ICheckBox extends IBasicComponent {
  size: string
  optionContentType: string
  vertical: boolean //是否垂直排列 单选框组和多选框组独有
  __slot__: {
    options: {
      label: string
      value: string
    }[]
  } //下拉选择或者级联选择选项
}
export interface IRadio extends IBasicComponent {
  vertical: boolean //是否垂直排列 单选框组和多选框组独有
  __slot__: {
    options: {
      label: string
      value: string
    }[]
  } //下拉选择或者级联选择选项
  // optionType: 'default' | 'button' //单选框组独有。选项样式
  size: string
}

// 通知方式
export interface INotify extends IBasicComponent {
  __slot__: {
    options: {
      label: string
      value: string
      disabled: boolean
    }[]
  }
}
export interface IAssociateData extends IBasicComponent {
  size: 'medium' | 'small' | 'mini' //按鈕大小
  __slot__: {
    default: string
  }
  dataConfig: IDataConfig
  icon: string
}
export type IAddress = IBasicComponent
export interface IEthnicity extends IBasicComponent {
  filterable: boolean //是否可搜索
}
export interface IOcr extends IBasicComponent {
  'show-word-limit': boolean
  maxlength: number
}

//目录树
export interface IDirectoryTree extends IBasicComponent {
  showAllLevels: boolean //展示全路径
  separator: string //分隔符
  filterable: boolean //搜索
  clearable: boolean //清空搜索条件
  resourceType: 'custom' | 'connector' //数据来源类型
  customValue?: ITreeNode[]
  connectorValue?: string //连接器id
}

// 填写人信息
export interface IImformation extends IBasicComponent {
  fieldVisible: {
    userFullname: boolean
    prjDepName: boolean
    createDate: boolean
    userTelephoneNum: boolean
  }
}

//组件配置类型
export interface IConfig {
  lead: boolean
  label: string //标题
  showLabel: boolean //是否显示标题
  tag: ITag // render函数使用这个标签渲染组件
  tagIcon: string //左侧组件的图标
  tagAttribute: ITagAttribute //每种组件的唯一标识
  dataType: IDataType //数据类型
  defaultValue?: any //默认值
  defaultValue2?: any
  required: boolean //是否必填
  span: number // 6 | 8 | 12 | 16 | 18 | 24 宽度,分别对应1/4,1/3,1/2,2/3,3/4,1(整行)
  optionType?: string // 单选框组的私有属性
  [key: string]: any
}

/*
 *流程设计
 */

export type IProcessDesign = {
  modelKey: string
  // modelName: string
  // modelType: string
  nodeList: INode[]
}
//审批人 '1'='用户自选  '2'= '指定人员' '3' = '与前置节点相同  '4'= 指定岗位 '5'= 指定岗位
export type IApproveType = '1' | '2' | '3' | '4' | '5'
// export enum EApproveType {
//   '用户自选' = '1',
//   '指定人员' = '2',
//   '与前置节点相同' = '3',
//   '指定岗位' = '4'
// }

//审批方式
export type IAssigneeType = '1' | '2' | '3'

//基础节点，所有节点都有的属性
export interface IBasic {
  id: string
  type: ENodeType
  title: string
  source?: string
  target?: string
}
//审批节点
export interface IApproveNode extends IBasic {
  nodeConfig: INodeConfig
}
//条件节点
export interface ICondition extends IBasic {
  isDefault: boolean
  condition: IRuleGroupItem[]
}
//分支节点
export interface IBranch extends IBasic {
  sort: number
  nodeList: INode[]
}
// route节点
export interface IRoute extends IBasic {
  branches: IBranch[]
}

export interface ISubmitFormPermission {
  hide: string[]
  modify: string[]
  readonly: string[]
}

// 连接器节点
export interface IConnectorNode extends IBasic {
  nodeConfig: IConnectorProperties
}

// 节点配置 node.nodeConfig
export interface INodeConfig {
  approvalType: IApproveType
  assignee: string
  assigneeUserFullName: string
  assigneeType: IAssigneeType
  assigneeLabel: string
  buttonList: INodeButton[]
  required: '0' | '1'
  formPermission: ISubmitFormPermission
  detailPermission: ISubmitFormPermission
  emptySolution: '0' | '1'
  emptyAssigneeUserName: string
  emptyAssigneeUserFullName: string
  // 抄送设置
  circulationParam: {
    notify: string
    receiverUserName: string
    receiverUserFullName: string
  }
  dueDateParam: {
    handingType: 'none' | 'skip' | 'notify' | 'assign'
    day: number
    hour: number
    assignee: {
      userName: string
      userFullname: string
    }
    notify: string
  }
  sameNodeId?: string
  sameNodeName?: string
  updatePermission?: '0' | '1'
  postName?: string
  postFullname?: string
  deptName?: string
  deptFullname?: string
  sourceNodeApprovalType?: Exclude<IApproveType, '3'>
}
//所有节点,开始节点，审批节点等
export type INode = IApproveNode | IBasic | ICondition | IBranch | IRoute
// 节点类型 node.type
export enum ENodeType {
  start = 'start',
  approval = 'approval',
  end = 'end',
  condition = 'condition',
  branch = 'branch',
  route = 'route',
  connector = 'connector'
}
// 操作权限 按钮枚举
export enum EButtonType {
  submit = 'submit',
  reject = 'reject',
  abandon = 'abandon',
  saveDraft = 'saveDraft'
}
// 操作权限 节点按钮配置
export interface INodeButton {
  code: string
  customCode: string
  text: string
  type: EButtonType
  targetNode?: string[]
  rejectType?: string
}

/*
 * 列表设计
 */
export enum EDisplayStatus {
  HIDE = '0',
  SHOW = '1'
}

export interface ITreeTable {
  field: string //目录树组件绑定的表单字段
  showAllLevels: boolean //展示全路径
  separator: string //分隔符
  filterable: boolean //搜索
  clearable: boolean //清空搜索条件
  resourceType: 'custom' | 'connector' //数据来源类型
  customValue?: ITreeNode[]
  connectorValue?: string //连接器id
}

export interface ITableDesign {
  fieldsTableData: ITableCol[]
  listName: string
  innerBtnTable: ITableBtn[]
  funcBtnTable: ITableBtn[]
  treeTable?: ITreeTable
}

export enum EButtonAction {
  VIEW = 'view',
  PRINTFILE = 'printFile',
  EDIT = 'edit',
  REMOVE = 'remove',
  ADD = 'add',
  IMPORTFILE = 'importFile',
  EXPORTFILE = 'exportFile',
  BATCHDELETE = 'batchDelete',
  POINT = 'point'
}
export interface ITableCol {
  id: string
  dataType: IDataType
  name: string
  type: string
  display: EDisplayStatus
  sort: '0' | '1'
  filter?: '0' | '1'
  width: number
  fixed: '0' | '1'
  align: 'center' | 'left' | 'right'
  tagAttribute?: ITagAttribute
}

export interface IConnectParam {
  paramId: string
  connectorParamName: string
  showName: string
  type: string
  columnType: string | null
  defaultValue: string | null
  parentId: string | null
  formField: string
  componentName: string
  valueType: '0' | '1' //0-取用input输入值defaultValue，1-取用映射表单组件formField
}

export interface IParamsMapOptions extends IConnectParam {
  isRequired: string
  options: ICurrentFormField[]
  columnName?: string | null //明细表列名
  error?: string
  customValue?: any //根据连接器配置的数据类型转换出的实际值
}

export interface ITableBtn {
  action: EButtonAction | string
  display: EDisplayStatus
  funcName: string
  showName: string
  type?: string
  icon?: string
  connectorId?: string
  connectorName?: string
  disabled?: boolean
  connectorParamMappings?: IConnectParam[]
}

export enum EBtnAuthType {
  INNER = 'innerBtnTable',
  FUNC = 'funcBtnTable'
}

/*
 *权限设置
 */
export interface IAuthDesign {
  dataAuth: 'public' | 'private'
  authGroup: IAuthConfig[]
}
export interface IAuthConfig {
  // isAllpermissions?: boolean
  roleDesignName: string
  roleDescription: string
  // roleOperatingAuth: IRoleOperatingAuth
  buttons: EButtonAction[]
  isAuth: EIsAuth
  authUser: IUser[]
  authDept: IDept[]
  authPost: IPost[]
}

type IRoleOperatingAuth = {
  funcBtnTable: EButtonAction[]
  innerBtnTable: EButtonAction[]
}

export enum EDialogActionType {
  ADD,
  EDIT
}

export enum EIsAuth {
  NO = '0',
  YES = '1'
}

export interface IAuthConfigVO extends IAuthConfig {
  userExpand: boolean
  groupExpand: boolean
  postExpand: boolean

  roleUsers: string
  roleUsersFullname: string
  roleGroup: string
  roleGroupCode: string
  roleGroupFullname: string
  rolePost: string
  rolePostFullname: string
}

export interface IAuthMember {
  fullname: string
  id?: string
  name?: string
  code?: string
}

export interface IUser extends IAuthMember {
  name: string //对应roleUsers
  fullname: string //对应roleUsersFullname
}

export interface IDept extends IAuthMember {
  id: string //对应roleGroup
  code: string //对应roleGroupCode
  fullname: string //对应roleGroupFullname
}

export interface IPost extends IAuthMember {
  id: string //对应rolePost
  fullname: string //对应rolePostFullname
}

/*
 * 发布设置
 */
export enum ECommonBool {
  YES = '1',
  NO = '0'
}

export enum ETimesLimit {
  NO_LIMIT = '0',
  ONLY_ONE = '1',
  ONCE_A_DAY = '2'
}

export interface IPublishDesign {
  isAuth: `${EIsAuth}`
  roleUsers: string
  roleUsersFullname: string
  openEffective: ECommonBool
  openLink: string
  fillingNumberOfTimes: ETimesLimit
  submitValidation: ECommonBool
}
/*
 * 拓展设置
 */
export type IExtraDesign = {
  printTemplates: IPrintTemplate[]
  businessRules: IBusinessRuleItem[]
}
/*
 *业务规则
 */
//每一条业务规则的数据格式
export interface IBusinessRuleItem {
  id: string
  ruleName: string //业务规则名称
  ruleGroup: IRuleGroupItem[] //条件组
  actionList: IActionItem[] //动作
}
//每个条件组的数据格式
export interface IRuleGroupItem {
  id: string
  rules: IRuleItem[]
}
//每条规则的数据格式
export interface IRuleItem {
  id: string
  label: string //每一项的label
  __vModel__: string // 唯一标识，表明是哪一个表单项
  rule: string //规则（大于，小于等）
  tagAttribute: string //组件类型
  value: any //规则值1
  value2: string //规则值2
  isDelete?: boolean //是否被删除
  isOptionChange?: boolean //是否选项更改
}

export interface IActionItem {
  id: string
  actionType: string
  actionField: string
  actionLabel: string
  isDelete?: boolean
}
/*
 * 打印模板
 */
export interface IPrintTemplate {
  fileToken: string
  fileName: string
}

// 明细表模板下载
export interface IDetailTemplate {
  detail: boolean
  field: string
  formKey: string
}
export type IFormulaType = {
  funcName: string
  valueArr: string[]
  labelArr: string[]
}
