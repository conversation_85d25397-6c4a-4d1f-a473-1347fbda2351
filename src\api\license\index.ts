import { request } from '@/fawkes'
import { IResponse } from '@/types'
export interface ILicenseData {
  consumerAmount: number | null
  consumerType: string | null
  extra: {
    cpuSerial: string | null
    snCode: string | null
    service: string | null
    onlineNum: number | null
    licenseInterface: string | null
    ext1: string | null
    ext2: string | null
    ext3: string | null
    ext4: string | null
    ext5: string | null
    isIgnorePeopleLimit: boolean
    isIgnoreTimeLimit: boolean
    licenseCode: string
  }
  holder: {
    name: string | null
    encoded: string | null
  }
  info: string
  issued: string
  issuer: { name: string | null; encoded: string | null }
  notAfter: string
  notBefore: string
  subject: string
}
export function queryLicenseData() {
  return request<IResponse<ILicenseData>>({
    url: '/licenseInfo',
    method: 'get'
  })
}

export function updateLicense(data: FormData) {
  return request<IResponse>({
    url: '/license/install',
    method: 'post',
    data: data,
    responseType: 'blob'
  })
}
