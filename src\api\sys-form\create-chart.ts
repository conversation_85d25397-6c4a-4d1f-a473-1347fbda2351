/*
 * @Author: your name
 * @Date: 2023-03-10 14:51:54
 * @LastEditTime: 2023-03-20 09:48:46
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \custom-app-plus\src\api\sys-form\create-chart.ts
 */
import { request } from '@/fawkes'
import type { IResponse } from '@/types/'
import { IRuleGroupItem } from '@/api/sys-form/appModuleDesigner.type'

// 创建报表获取选项接口
export interface IQueryChartConfig {
  dimension: IFieldItem[]
  filter: IFieldItem[]
  formType: 1 | 0
  historyDimensionColumnList: IFieldItem[]
  index: IFieldItem[]
  indexConfig: IFieldItem[]
}
// 指标或维度的字段
export interface IFieldItem {
  columnField: string
  columnName: string
  dataType: string
  deleteFlag: string
  indexType: string
}
// 初始化页面配置选项
export async function queryConfigChart(formKey: string, chartId: string) {
  const res = await request<IResponse<IQueryChartConfig>>({
    url: '/sys-form/form/chart/dimension',
    method: 'get',
    params: {
      formKey,
      chartId
    }
  })
  return {
    dimension: res.data.dimension,
    index: res.data.index
  }
}

// 获取报表数据
export interface IChartConfig {
  formKey: string
  chartType: IChartType
  chartName: string
  dimensions: IDimension[]
  indicators: IIndicator[]
  conditionList: IRuleGroupItem[]
}
export type IChartType = 'bar' | 'ind' | 'col' | 'line' | 'area' | 'pie' | 'biaxial' | 'radar' | 'table' | ''
export interface IDimension {
  columnField: string
  columnName: string
  dataType: string
  dateCycle: IDateCycle
  dateCycleLabel: IDateCycleLabel
}
export interface IIndicator {
  columnField: string
  columnName: string
  dataType: string
  indexType: string
  summaryMethod: ISummaryMethod
  summaryMethodLabel: ISummaryMethodLabel
  metricType: IMetricType
}
export type IDateCycle = 'day' | 'week' | 'month' | 'season' | 'year' | '' | string
export type IDateCycleLabel = '年-月-日' | '年-周' | '年-月' | '年-季' | '年' | '' | string
export type IMetricType = 'left' | 'right' | '' | string
export type ISummaryMethod = 'sum' | 'avg' | 'max' | 'min' | 'count' | '' | string
export type ISummaryMethodLabel = '求和' | '平均值' | '最大值' | '最小值' | '次数' | '' | string
// 接口返回渲染报表数据
export interface IChartData {
  series: Array<ISeriesItem>
  x: Array<string>
  x1: Array<string>
}
export interface ISeriesItem {
  columnName: string
  ifPercent: null | string
  indicatorResult: Array<number>
  metricType: string
  total: null | string
}
// 获取chart数据
export function queryChartItem(data: IChartConfig) {
  return request<IResponse<IChartData>>({
    url: 'sys-form/form/analyzeData/analyze',
    method: 'post',
    data
  })
}

// 新增或编辑的请求数据
export interface IEditOrAddData {
  chartType: IChartType
  formKey: string
  groupId: string
  moduleId: string
  name: string
  chartCustomIndexVoList: INewIndicator[]
  paramJson: string
  styleJson: string
  createBy?: string
  createByFullName?: string
  createDate?: string
  createIcon?: null | string
  id?: string
  deleteFlag?: number
}
// 传入数据库的新指标
export interface INewIndicator {
  customIndexName: string
  customIndexFormula: string
}
// 新增统计图
export function createChart(data: IEditOrAddData, url: string, type: string) {
  return request<IResponse<null>>({
    url: url,
    method: type,
    data
  })
}

// 已创建报表回显的配置项
export interface IChartInfo {
  chartType: string
  createBy: string
  createByFullName: string
  createDate: string
  createIcon: null | string
  customIndexJson: null
  dataAuthType: null
  deleteFlag: number
  formKey: string
  groupId: string
  id: string
  method: null
  moduleId: string
  name: string
  paramJson: string
  styleJson: string
  updateBy: string
  updateDate: string
  checked?: boolean
}
// 获取已创建的图标数据
export function queryChartInfo(id: string) {
  return request<IResponse<IChartInfo>>({
    url: '/sys-form/form/chart/info',
    method: 'get',
    params: {
      chartId: id
    }
  })
}
